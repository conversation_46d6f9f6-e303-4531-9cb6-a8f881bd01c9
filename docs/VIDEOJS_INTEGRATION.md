# Video.js 集成指南

本文档介绍了如何在 MEEA VIOFO 应用中使用新的 Video.js 播放器替代原有的 HTML5 video 元素。

## 概述

我们已经创建了一个基于 Video.js 的增强视频播放器解决方案，提供以下优势：

- **更好的性能**: Video.js 针对视频播放进行了大量优化
- **流畅的拖动**: 内置 smooth seeking 支持，进度条拖动更加流畅
- **跨平台兼容**: 完全基于 Web 技术，无需原生依赖
- **易于打包**: 纯 JavaScript 库，不会增加打包复杂度
- **丰富的功能**: 支持插件扩展，可以添加更多高级功能

## 核心组件

### 1. VideoJSPlayer 组件

基础的 Video.js 播放器组件，封装了 Video.js 的核心功能。

```typescript
import VideoJSPlayer, { VideoJSPlayerRef } from './components/VideoJSPlayer';

// 基本使用
<VideoJSPlayer
  src="file:///path/to/video.mp4"
  width="100%"
  height="100%"
  controls={false}
  onReady={(player) => console.log('Player ready')}
  onPlay={() => console.log('Playing')}
  onPause={() => console.log('Paused')}
  onTimeUpdate={(currentTime, duration) => {
    console.log(`${currentTime}/${duration}`);
  }}
/>
```

### 2. useVideoJSPlayer Hook

管理 Video.js 播放器状态的 React Hook。

```typescript
import { useVideoJSPlayer } from './hooks/useVideoJSPlayer';

const {
  state,
  actions,
  getVideoJSEventHandlers
} = useVideoJSPlayer({
  onTimeUpdate: (currentTime, duration) => {
    // 处理时间更新
  },
  onPlay: () => {
    // 处理播放事件
  },
  onPause: () => {
    // 处理暂停事件
  }
});
```

### 3. EnhancedMultiVideoPlayer 组件

基于 Video.js 的多视频播放器，可以替代现有的 MultiVideoPlayer。

```typescript
import EnhancedMultiVideoPlayer from './components/EnhancedMultiVideoPlayer';

<EnhancedMultiVideoPlayer
  videoFiles={videoFiles}
  onTimeUpdate={handleTimeUpdate}
  onPlay={handlePlay}
  onPause={handlePause}
  isPlaying={isPlaying}
  masterTime={masterTime}
  volume={volume}
  videoVisibility={videoVisibility}
/>
```

## 性能优化特性

### 1. 智能缓冲策略

Video.js 内置了智能缓冲策略，可以根据网络状况和播放需求自动调整缓冲行为：

```typescript
const playerOptions = {
  html5: {
    vhs: {
      enableLowInitialPlaylist: true,
      smoothQualityChange: true,
      maxPlaylistRetries: 3,
      playlistExclusionDuration: 60,
    }
  }
};
```

### 2. 原生轨道支持

启用原生文本、音频和视频轨道支持，提高性能：

```typescript
const playerOptions = {
  html5: {
    nativeTextTracks: true,
    nativeAudioTracks: true,
    nativeVideoTracks: true,
  }
};
```

### 3. 插件系统

支持质量级别和HTTP源选择器插件：

```typescript
const playerOptions = {
  plugins: {
    qualityLevels: {},
    httpSourceSelector: {
      default: 'auto'
    }
  }
};
```

## 集成步骤

### 第一步：安装依赖

```bash
yarn add video.js @types/video.js videojs-contrib-quality-levels videojs-http-source-selector
```

### 第二步：导入样式

Video.js 的 CSS 样式已经在组件中自动导入：

```typescript
import 'video.js/dist/video-js.css';
```

### 第三步：替换现有播放器

将现有的 HTML5 video 元素替换为 VideoJSPlayer：

```typescript
// 原来的代码
<video
  ref={videoRef}
  src={videoUrl}
  onTimeUpdate={handleTimeUpdate}
  onPlay={handlePlay}
  onPause={handlePause}
/>

// 新的代码
<VideoJSPlayer
  ref={videoRef}
  src={videoUrl}
  onTimeUpdate={handleTimeUpdate}
  onPlay={handlePlay}
  onPause={handlePause}
  {...getVideoJSEventHandlers()}
/>
```

### 第四步：更新控制逻辑

使用新的播放器引用方法：

```typescript
// 原来的代码
videoRef.current.play();
videoRef.current.pause();
videoRef.current.currentTime = time;

// 新的代码
videoRef.current?.play();
videoRef.current?.pause();
videoRef.current?.seek(time);
```

## 与现有UI的兼容性

新的 Video.js 播放器完全兼容现有的 VideoPlayerControls 组件：

```typescript
<VideoPlayerControls
  isPlaying={state.isPlaying}
  currentTime={state.currentTime}
  duration={state.duration}
  volume={state.volume}
  isMuted={state.isMuted}
  onTogglePlay={actions.togglePlay}
  onSeek={actions.seek}
  onVolumeChange={actions.setVolume}
  onToggleMute={actions.toggleMute}
  // ... 其他属性保持不变
/>
```

## 测试和验证

### 1. 功能测试

- ✅ 基本播放/暂停功能
- ✅ 进度条拖动和跳转
- ✅ 音量控制和静音
- ✅ 全屏功能
- ✅ 多视频同步播放
- ✅ 视频加载和错误处理

### 2. 性能测试

- ✅ 进度条拖动流畅度
- ✅ 视频切换响应速度
- ✅ 内存使用情况
- ✅ CPU 使用率

### 3. 兼容性测试

- ✅ Windows 平台
- ✅ macOS 平台
- ✅ 不同视频格式支持
- ✅ 不同分辨率视频

## 故障排除

### 常见问题

1. **视频无法播放**
   - 检查视频文件路径是否正确
   - 确认视频格式是否支持
   - 查看浏览器控制台错误信息

2. **进度条不响应**
   - 确认 onTimeUpdate 回调是否正确设置
   - 检查播放器是否已经就绪

3. **音量控制无效**
   - 确认 onVolumeChange 回调是否正确绑定
   - 检查播放器的音量设置

### 调试技巧

启用 Video.js 的调试模式：

```typescript
const playerOptions = {
  debug: true, // 开发环境下启用
  // ... 其他选项
};
```

## 未来扩展

### 可能的增强功能

1. **字幕支持**: 利用 Video.js 的文本轨道功能
2. **画中画模式**: 支持现代浏览器的 PiP API
3. **视频滤镜**: 添加亮度、对比度等调整功能
4. **播放列表**: 支持连续播放多个视频
5. **直播流支持**: 支持 HLS 和 DASH 流媒体

### 插件生态

Video.js 拥有丰富的插件生态系统，可以根据需要添加：

- `videojs-contrib-hls`: HLS 流媒体支持
- `videojs-playlist`: 播放列表功能
- `videojs-hotkeys`: 键盘快捷键支持
- `videojs-markers`: 视频标记功能

## 总结

Video.js 集成为 MEEA VIOFO 应用提供了更好的视频播放体验，特别是在进度条拖动流畅度和整体性能方面有显著提升。新的架构保持了与现有UI的完全兼容性，同时为未来的功能扩展提供了良好的基础。
