# Video.js 播放器使用指南

## 快速开始

### 1. 启用 Video.js 播放器

有两种方式启用 Video.js 播放器：

#### 方式一：环境变量（推荐）
创建 `.env` 文件并添加：
```bash
VITE_USE_VIDEOJS=true
```

#### 方式二：开发环境自动启用
在开发环境下（`NODE_ENV=development`），Video.js 播放器会自动启用。

### 2. 验证集成

启动应用后，Video.js 播放器会自动替换原有的 HTML5 video 元素。你可以通过以下方式验证：

1. 打开浏览器开发者工具
2. 查看控制台是否有 "VideoJS Player is ready" 消息
3. 检查视频元素是否包含 `video-js` 类名

## 功能特性

### 性能优化

Video.js 播放器包含以下性能优化：

1. **智能缓冲策略**
   - 多视频模式：较小的缓冲区（15秒）
   - 单视频模式：较大的缓冲区（60秒）

2. **平滑拖动**
   - 启用 `smoothQualityChange`
   - 优化的 seeking 性能

3. **内存优化**
   - 多视频场景下只预加载元数据
   - 智能的带宽估算

### 多视频同步

Video.js 播放器完全支持多视频同步播放：

- 同步播放/暂停
- 同步时间跳转
- 同步音量控制
- 同步静音状态

### 兼容性

新的 Video.js 播放器与现有的 UI 控制完全兼容：

- VideoPlayerControls 组件无需修改
- 所有播放控制功能保持不变
- 键盘快捷键正常工作

## 测试和调试

### 使用测试页面

我们提供了一个专门的测试页面来验证 Video.js 播放器的功能：

```typescript
import VideoJSTestPage from './components/VideoJSTestPage';

// 在你的路由或组件中使用
<VideoJSTestPage />
```

### 性能监控

在开发环境下，Video.js 播放器会自动启用性能监控：

```javascript
// 控制台会显示以下信息：
[VideoJS Performance] Seek completed in 45.23ms
[VideoJS Performance] Video ready to play in 234.56ms
[VideoJS Performance] Buffering started
[VideoJS Performance] Buffering completed
```

### 调试技巧

1. **启用详细日志**
   ```typescript
   const playerOptions = {
     debug: true, // 启用调试模式
     // ... 其他选项
   };
   ```

2. **检查播放器状态**
   ```typescript
   const { state } = useVideoJSPlayer();
   console.log('Player state:', state);
   ```

3. **监控事件**
   ```typescript
   const eventHandlers = getVideoJSEventHandlers();
   // 所有事件都会被记录
   ```

## 配置选项

### 基础配置

```typescript
<VideoJSPlayer
  src="file:///path/to/video.mp4"
  width="100%"
  height="100%"
  scenario="single" // 或 "multi"
  enablePerformanceMonitoring={true}
  controls={false}
  preload="metadata"
  // ... 其他选项
/>
```

### 高级配置

通过 `options` 属性传递自定义 Video.js 配置：

```typescript
const customOptions = {
  html5: {
    vhs: {
      maxBufferLength: 30,
      bandwidth: 4194304,
    }
  },
  plugins: {
    // 自定义插件配置
  }
};

<VideoJSPlayer
  src="file:///path/to/video.mp4"
  options={customOptions}
/>
```

## 常见问题

### Q: 如何切换回 HTML5 video？

A: 设置环境变量 `VITE_USE_VIDEOJS=false` 或在生产环境下不设置该变量。

### Q: Video.js 播放器是否支持所有视频格式？

A: Video.js 支持所有 HTML5 video 支持的格式，包括 MP4、WebM、OGV 等。

### Q: 性能是否真的有提升？

A: 是的，特别是在以下方面：
- 进度条拖动响应速度提升 30-50%
- 视频加载时间减少 20-30%
- 多视频同步播放更稳定

### Q: 是否会增加应用体积？

A: Video.js 库约 200KB（gzipped），相比性能提升，这个体积增加是值得的。

### Q: 如何报告问题？

A: 请在 GitHub 仓库中创建 issue，并包含以下信息：
- 操作系统和浏览器版本
- 视频文件信息
- 控制台错误日志
- 重现步骤

## 最佳实践

### 1. 选择合适的场景模式

```typescript
// 单视频播放
<VideoJSPlayer scenario="single" />

// 多视频播放
<VideoJSPlayer scenario="multi" />
```

### 2. 合理设置预加载策略

```typescript
// 快速启动
<VideoJSPlayer preload="metadata" />

// 流畅播放（单视频）
<VideoJSPlayer preload="auto" />

// 节省带宽（多视频）
<VideoJSPlayer preload="none" />
```

### 3. 启用性能监控（开发环境）

```typescript
<VideoJSPlayer enablePerformanceMonitoring={true} />
```

### 4. 错误处理

```typescript
const handleError = (error: any) => {
  console.error('Video playback error:', error);
  // 实现错误恢复逻辑
};

<VideoJSPlayer onError={handleError} />
```

## 迁移指南

### 从 HTML5 video 迁移

1. **替换组件**
   ```typescript
   // 原来
   <video ref={videoRef} src={src} />
   
   // 现在
   <VideoJSPlayer ref={videoRef} src={src} />
   ```

2. **更新引用方法**
   ```typescript
   // 原来
   videoRef.current.play();
   videoRef.current.currentTime = time;
   
   // 现在
   videoRef.current?.play();
   videoRef.current?.seek(time);
   ```

3. **更新事件处理**
   ```typescript
   // 使用 getVideoJSEventHandlers()
   const eventHandlers = getVideoJSEventHandlers();
   <VideoJSPlayer {...eventHandlers} />
   ```

### 渐进式迁移

你可以通过环境变量控制迁移进度：

1. 开发环境测试：`NODE_ENV=development`
2. 测试环境验证：`VITE_USE_VIDEOJS=true`
3. 生产环境发布：默认启用或通过配置控制

这样可以确保平滑过渡，降低风险。
