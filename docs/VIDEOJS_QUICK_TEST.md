# Video.js 快速测试指南

## 🚀 问题已修复

之前遇到的插件错误已经解决：
- ❌ 移除了有问题的 `videojs-contrib-quality-levels` 插件
- ❌ 移除了有问题的 `videojs-http-source-selector` 插件
- ✅ 使用简化的 Video.js 配置，专注于核心功能
- ✅ 构建测试通过

## 🧪 如何测试 Video.js 播放器

### 方法一：使用内置测试页面（推荐）

1. **启动应用**
   ```bash
   yarn dev
   ```

2. **进入测试模式**
   - 在左侧文件管理面板中，你会看到一个蓝色的 "Video.js 测试" 按钮
   - 点击该按钮进入测试模式

3. **测试功能**
   - 输入视频文件的完整路径（例如：`/Users/<USER>/video.mp4`）
   - 点击 "加载视频" 按钮
   - 使用播放控制按钮测试各种功能

### 方法二：在主应用中测试

1. **直接使用应用**
   ```bash
   # 启动应用（Video.js 默认启用）
   yarn dev
   ```

2. **正常使用应用**
   - 选择视频文件夹
   - 播放视频时会自动使用 Video.js 播放器
   - 观察控制台是否有 "VideoJS Player is ready" 消息

### 如何禁用 Video.js（如需回退）

如果需要回退到 HTML5 video：
```bash
# 创建 .env 文件并禁用 Video.js
echo "VITE_USE_VIDEOJS=false" > .env

# 重启应用
yarn dev
```

## 🔍 验证 Video.js 是否正常工作

### 成功标志
- ✅ 控制台显示 "VideoJS Player is ready"
- ✅ 视频元素包含 `video-js` 类名
- ✅ 播放器状态显示为 "就绪"
- ✅ 没有插件相关错误

### 性能测试
在测试页面中，你可以：
- 🎯 **性能测试** - 自动测试快速跳转功能
- 🎮 **播放控制测试** - 测试播放/暂停循环
- 🔊 **音量控制测试** - 测试音量和静音功能

## 📊 性能对比

使用 Video.js 播放器后，你应该能感受到：

### 进度条拖动
- **HTML5 video**: 拖动后有明显延迟，可能卡顿
- **Video.js**: 拖动响应更快，更流畅

### 视频加载
- **HTML5 video**: 加载时间较长，缓冲策略简单
- **Video.js**: 智能缓冲，加载更快

### 多视频播放
- **HTML5 video**: 同步可能不稳定
- **Video.js**: 更好的同步控制

## 🐛 故障排除

### 如果遇到问题

1. **检查控制台错误**
   - 打开浏览器开发者工具
   - 查看是否有 JavaScript 错误

2. **确认 Video.js 版本**
   - 控制台应该显示 Video.js 版本信息
   - 当前使用版本：8.23.3

3. **测试简单视频文件**
   - 使用标准的 MP4 文件进行测试
   - 确保文件路径正确

4. **回滚到 HTML5 video**
   ```bash
   # 如果需要回滚
   echo "VITE_USE_VIDEOJS=false" > .env
   # 然后重启应用
   yarn dev
   ```

## 🎯 测试重点

### 必测功能
- [ ] 视频加载和播放
- [ ] 进度条拖动响应速度
- [ ] 播放/暂停控制
- [ ] 音量控制和静音
- [ ] 多视频同步播放（如果适用）

### 性能测试
- [ ] 快速连续跳转（10s, 30s, 60s, 120s, 0s）
- [ ] 播放/暂停循环测试
- [ ] 音量调节测试
- [ ] 长时间播放稳定性

## 📝 反馈

测试完成后，请注意：

### 性能改进
- 进度条拖动是否更流畅？
- 视频加载是否更快？
- 多视频播放是否更稳定？

### 用户体验
- 界面是否保持一致？
- 所有控制功能是否正常？
- 是否有任何回归问题？

## 🔄 下一步

如果测试顺利：
1. 可以在生产环境中启用 Video.js
2. 监控用户反馈和性能指标
3. 考虑添加更多 Video.js 插件功能

如果遇到问题：
1. 记录具体的错误信息
2. 提供重现步骤
3. 可以随时回滚到 HTML5 video

---

**注意**: 这是一个渐进式集成方案，可以随时在 Video.js 和 HTML5 video 之间切换，确保应用的稳定性。
