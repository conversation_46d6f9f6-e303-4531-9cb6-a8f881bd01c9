/**
 * Video.js 配置测试工具
 * 用于验证 Video.js 是否正确启用
 */

// 检查 Video.js 是否启用
export const isVideoJSEnabled = (): boolean => {
  return process.env.VITE_USE_VIDEOJS !== 'false';
};

// 获取当前播放器类型
export const getPlayerType = (): 'videojs' | 'html5' => {
  return isVideoJSEnabled() ? 'videojs' : 'html5';
};

// 记录播放器配置信息
export const logPlayerConfig = (): void => {
  const playerType = getPlayerType();
  const envVar = process.env.VITE_USE_VIDEOJS;
  
  console.log('🎬 [Video Player Config]');
  console.log(`   播放器类型: ${playerType === 'videojs' ? 'Video.js' : 'HTML5 Video'}`);
  console.log(`   环境变量 VITE_USE_VIDEOJS: ${envVar || '未设置'}`);
  console.log(`   默认行为: Video.js 启用 (除非明确设置为 'false')`);
  
  if (playerType === 'videojs') {
    console.log('✅ Video.js 播放器已启用');
  } else {
    console.log('⚠️  已回退到 HTML5 video 播放器');
  }
};

// 在开发环境下自动记录配置
if (process.env.NODE_ENV === 'development') {
  // 延迟执行，确保在应用启动后记录
  setTimeout(() => {
    logPlayerConfig();
  }, 1000);
}
