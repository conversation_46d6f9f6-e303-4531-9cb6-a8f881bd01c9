import { useState, useCallback, useRef, useEffect } from 'react';
import videojs from 'video.js';
import { VideoJSPlayerRef } from '../components/VideoJSPlayer';

export interface VideoJSPlayerState {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  isMuted: boolean;
  isFullscreen: boolean;
  isLoading: boolean;
  hasError: boolean;
  errorMessage: string;
  playbackRate: number;
  isReady: boolean;
}

export interface VideoJSPlayerActions {
  togglePlay: () => void;
  play: () => Promise<void>;
  pause: () => void;
  seek: (time: number) => void;
  setVolume: (volume: number) => void;
  toggleMute: () => void;
  skipTime: (seconds: number) => void;
  toggleFullscreen: () => void;
  setPlaybackRate: (rate: number) => void;
  setDuration: (duration: number) => void;
  setCurrentTime: (time: number) => void;
  setIsPlaying: (playing: boolean) => void;
  setIsLoading: (loading: boolean) => void;
  setError: (error: string) => void;
  clearError: () => void;
}

export interface UseVideoJSPlayerOptions {
  onTimeUpdate?: (currentTime: number, duration: number) => void;
  onPlay?: () => void;
  onPause?: () => void;
  onSeek?: (time: number) => void;
  onVolumeChange?: (volume: number, muted: boolean) => void;
  onError?: (error: string) => void;
  onReady?: (player: videojs.Player) => void;
}

export const useVideoJSPlayer = (options: UseVideoJSPlayerOptions = {}) => {
  const {
    onTimeUpdate,
    onPlay,
    onPause,
    onSeek,
    onVolumeChange,
    onError,
    onReady
  } = options;

  const [state, setState] = useState<VideoJSPlayerState>({
    isPlaying: false,
    currentTime: 0,
    duration: 0,
    volume: 1,
    isMuted: false,
    isFullscreen: false,
    isLoading: false,
    hasError: false,
    errorMessage: '',
    playbackRate: 1,
    isReady: false
  });

  const playerRef = useRef<VideoJSPlayerRef | null>(null);
  const playerInstanceRef = useRef<videojs.Player | null>(null);

  // 播放控制
  const play = useCallback(async () => {
    if (playerRef.current) {
      try {
        await playerRef.current.play();
        setState(prev => ({ ...prev, isPlaying: true }));
        onPlay?.();
      } catch (error) {
        console.error('Play failed:', error);
        setError(`播放失败: ${error}`);
      }
    }
  }, [onPlay]);

  const pause = useCallback(() => {
    if (playerRef.current) {
      playerRef.current.pause();
      setState(prev => ({ ...prev, isPlaying: false }));
      onPause?.();
    }
  }, [onPause]);

  const togglePlay = useCallback(() => {
    if (state.isPlaying) {
      pause();
    } else {
      play();
    }
  }, [state.isPlaying, play, pause]);

  // 时间控制
  const seek = useCallback((time: number) => {
    if (playerRef.current) {
      playerRef.current.seek(time);
      setState(prev => ({ ...prev, currentTime: time }));
      onSeek?.(time);
      onTimeUpdate?.(time, state.duration);
    }
  }, [state.duration, onSeek, onTimeUpdate]);

  const skipTime = useCallback((seconds: number) => {
    const newTime = Math.max(0, Math.min(state.duration, state.currentTime + seconds));
    seek(newTime);
  }, [state.currentTime, state.duration, seek]);

  // 音量控制
  const setVolume = useCallback((volume: number) => {
    const clampedVolume = Math.max(0, Math.min(1, volume));
    if (playerRef.current) {
      playerRef.current.setVolume(clampedVolume);
      setState(prev => ({ ...prev, volume: clampedVolume }));
      onVolumeChange?.(clampedVolume, state.isMuted);
    }
  }, [state.isMuted, onVolumeChange]);

  const toggleMute = useCallback(() => {
    if (playerRef.current) {
      const newMuted = !state.isMuted;
      playerRef.current.setMuted(newMuted);
      setState(prev => ({ ...prev, isMuted: newMuted }));
      onVolumeChange?.(state.volume, newMuted);
    }
  }, [state.isMuted, state.volume, onVolumeChange]);

  // 全屏控制
  const toggleFullscreen = useCallback(() => {
    if (playerInstanceRef.current) {
      if (playerInstanceRef.current.isFullscreen()) {
        playerInstanceRef.current.exitFullscreen();
      } else {
        playerInstanceRef.current.requestFullscreen();
      }
    }
  }, []);

  // 播放速度控制
  const setPlaybackRate = useCallback((rate: number) => {
    if (playerInstanceRef.current) {
      playerInstanceRef.current.playbackRate(rate);
      setState(prev => ({ ...prev, playbackRate: rate }));
    }
  }, []);

  // 状态设置
  const setIsPlaying = useCallback((playing: boolean) => {
    setState(prev => ({ ...prev, isPlaying: playing }));
  }, []);

  const setIsLoading = useCallback((loading: boolean) => {
    setState(prev => ({ ...prev, isLoading: loading }));
  }, []);

  const setError = useCallback((error: string) => {
    setState(prev => ({ 
      ...prev, 
      hasError: true, 
      errorMessage: error,
      isLoading: false 
    }));
    onError?.(error);
  }, [onError]);

  const clearError = useCallback(() => {
    setState(prev => ({ 
      ...prev, 
      hasError: false, 
      errorMessage: '' 
    }));
  }, []);

  const setDuration = useCallback((duration: number) => {
    setState(prev => ({ ...prev, duration }));
  }, []);

  const setCurrentTime = useCallback((time: number) => {
    setState(prev => ({ ...prev, currentTime: time }));
  }, []);

  // Video.js 播放器事件处理器
  const getVideoJSEventHandlers = useCallback(() => {
    return {
      onReady: (player: videojs.Player) => {
        playerInstanceRef.current = player;
        setState(prev => ({ ...prev, isReady: true, isLoading: false }));
        onReady?.(player);
      },
      onPlay: () => setIsPlaying(true),
      onPause: () => setIsPlaying(false),
      onTimeUpdate: (currentTime: number, duration: number) => {
        setCurrentTime(currentTime);
        setDuration(duration);
        onTimeUpdate?.(currentTime, duration);
      },
      onSeeking: () => {
        // 可以在这里添加seeking状态处理
      },
      onSeeked: () => {
        // 可以在这里添加seeked状态处理
      },
      onLoadedMetadata: () => {
        setState(prev => ({ ...prev, isLoading: false }));
      },
      onLoadStart: () => {
        setState(prev => ({ 
          ...prev, 
          isLoading: true, 
          hasError: false, 
          errorMessage: '' 
        }));
      },
      onCanPlay: () => setIsLoading(false),
      onError: (error: any) => {
        setError(`视频加载失败: ${error?.message || '未知错误'}`);
      },
      onVolumeChange: (volume: number, muted: boolean) => {
        setState(prev => ({ 
          ...prev, 
          volume, 
          isMuted: muted 
        }));
        onVolumeChange?.(volume, muted);
      },
      onDurationChange: (duration: number) => {
        setDuration(duration);
      }
    };
  }, [setCurrentTime, setDuration, setIsPlaying, setIsLoading, setError, onTimeUpdate, onVolumeChange, onReady]);

  // 全屏状态监听
  const handleFullscreenChange = useCallback(() => {
    if (playerInstanceRef.current) {
      const isFullscreen = playerInstanceRef.current.isFullscreen();
      setState(prev => ({ ...prev, isFullscreen }));
    }
  }, []);

  // 监听全屏状态变化
  useEffect(() => {
    const player = playerInstanceRef.current;
    if (!player) return;

    player.on('fullscreenchange', handleFullscreenChange);
    
    return () => {
      player.off('fullscreenchange', handleFullscreenChange);
    };
  }, [handleFullscreenChange, state.isReady]);

  const actions: VideoJSPlayerActions = {
    togglePlay,
    play,
    pause,
    seek,
    setVolume,
    toggleMute,
    skipTime,
    toggleFullscreen,
    setPlaybackRate,
    setDuration,
    setCurrentTime,
    setIsPlaying,
    setIsLoading,
    setError,
    clearError
  };

  return {
    state,
    actions,
    playerRef,
    getVideoJSEventHandlers,
    handleFullscreenChange
  };
};
