import videojs from 'video.js';

/**
 * Video.js 性能优化配置
 */
export const VIDEOJS_PERFORMANCE_CONFIG: videojs.PlayerOptions = {
  // 基础配置
  controls: false,
  fluid: true,
  responsive: true,
  preload: 'metadata',
  playsinline: true,
  
  // HTML5 特定优化
  html5: {
    // VHS (Video HTTP Streaming) 优化
    vhs: {
      // 启用低初始播放列表，减少启动延迟
      enableLowInitialPlaylist: true,
      
      // 启用平滑质量切换
      smoothQualityChange: true,
      
      // 优化缓冲策略
      maxPlaylistRetries: 3,
      playlistExclusionDuration: 60,
      
      // 启用快速质量切换
      fastQualityChange: true,
      
      // 优化带宽估算
      bandwidth: 4194304, // 4 Mbps 初始带宽估算
      
      // 缓冲区配置
      maxBufferLength: 30, // 最大缓冲30秒
      maxMaxBufferLength: 120, // 绝对最大缓冲120秒
      
      // 启用低延迟模式
      liveRangeSafeTimeDelta: 30,
      
      // 优化播放列表加载
      useDevicePixelRatio: true,
    },
    
    // 启用原生轨道支持，提高性能
    nativeTextTracks: true,
    nativeAudioTracks: true,
    nativeVideoTracks: true,
    
    // 启用原生HLS支持（如果可用）
    overrideNative: false,
    
    // 启用硬件加速
    enableSourceset: true,
  },
  
  // 技术栈优先级配置
  techOrder: ['html5'],
  
  // 源配置
  sources: [],
  
  // 插件配置
  plugins: {
    // 质量级别插件
    qualityLevels: {},
    
    // HTTP源选择器插件
    httpSourceSelector: {
      default: 'auto'
    }
  },
  
  // 性能监控
  debug: process.env.NODE_ENV === 'development',
  
  // 语言配置
  language: 'zh-CN',
  
  // 响应式配置
  breakpoints: {
    tiny: 300,
    xsmall: 400,
    small: 500,
    medium: 600,
    large: 700,
    xlarge: 800,
    huge: 900
  },
  
  // 用户活动检测
  inactivityTimeout: 3000, // 3秒后隐藏控制栏
  
  // 错误处理
  suppressNotSupportedError: true,
  
  // 实验性功能
  experimentalSvgIcons: false,
  
  // 可访问性
  textTrackDisplay: true,
  
  // 播放器尺寸
  aspectRatio: '16:9',
  
  // 自动播放策略
  autoplay: false,
  muted: false,
  
  // 循环播放
  loop: false,
  
  // 音量配置
  volume: 1.0,
  
  // 播放速度
  playbackRates: [0.5, 1, 1.25, 1.5, 2],
  
  // 全屏配置
  fullscreen: {
    options: {
      navigationUI: 'hide'
    }
  }
};

/**
 * 针对多视频播放的优化配置
 */
export const MULTI_VIDEO_CONFIG: Partial<videojs.PlayerOptions> = {
  // 多视频场景下的特殊优化
  preload: 'metadata', // 只预加载元数据，减少内存使用
  
  html5: {
    ...VIDEOJS_PERFORMANCE_CONFIG.html5,
    vhs: {
      ...VIDEOJS_PERFORMANCE_CONFIG.html5?.vhs,
      // 多视频时减少缓冲区大小
      maxBufferLength: 15,
      maxMaxBufferLength: 60,
      // 降低初始带宽估算
      bandwidth: 2097152, // 2 Mbps
    }
  },
  
  // 禁用一些不必要的功能以提高性能
  textTrackDisplay: false,
  
  // 更短的不活动超时
  inactivityTimeout: 2000,
};

/**
 * 针对单视频播放的优化配置
 */
export const SINGLE_VIDEO_CONFIG: Partial<videojs.PlayerOptions> = {
  // 单视频场景下的优化
  preload: 'auto', // 可以预加载更多内容
  
  html5: {
    ...VIDEOJS_PERFORMANCE_CONFIG.html5,
    vhs: {
      ...VIDEOJS_PERFORMANCE_CONFIG.html5?.vhs,
      // 单视频时可以使用更大的缓冲区
      maxBufferLength: 60,
      maxMaxBufferLength: 180,
      // 更高的初始带宽估算
      bandwidth: 8388608, // 8 Mbps
    }
  },
  
  // 启用所有功能
  textTrackDisplay: true,
  
  // 标准不活动超时
  inactivityTimeout: 3000,
};

/**
 * 获取针对特定场景优化的配置
 */
export const getOptimizedConfig = (
  scenario: 'single' | 'multi' = 'single',
  customOptions: Partial<videojs.PlayerOptions> = {}
): videojs.PlayerOptions => {
  const baseConfig = VIDEOJS_PERFORMANCE_CONFIG;
  const scenarioConfig = scenario === 'multi' ? MULTI_VIDEO_CONFIG : SINGLE_VIDEO_CONFIG;
  
  return {
    ...baseConfig,
    ...scenarioConfig,
    ...customOptions,
    // 深度合并 html5 配置
    html5: {
      ...baseConfig.html5,
      ...scenarioConfig.html5,
      ...customOptions.html5,
      vhs: {
        ...baseConfig.html5?.vhs,
        ...scenarioConfig.html5?.vhs,
        ...customOptions.html5?.vhs,
      }
    },
    // 深度合并插件配置
    plugins: {
      ...baseConfig.plugins,
      ...scenarioConfig.plugins,
      ...customOptions.plugins,
    }
  };
};

/**
 * 性能监控工具
 */
export const createPerformanceMonitor = (player: videojs.Player) => {
  if (process.env.NODE_ENV !== 'development') {
    return;
  }
  
  let seekStartTime: number;
  let playStartTime: number;
  
  // 监控seeking性能
  player.on('seeking', () => {
    seekStartTime = performance.now();
  });
  
  player.on('seeked', () => {
    if (seekStartTime) {
      const seekTime = performance.now() - seekStartTime;
      console.log(`[VideoJS Performance] Seek completed in ${seekTime.toFixed(2)}ms`);
    }
  });
  
  // 监控播放启动性能
  player.on('loadstart', () => {
    playStartTime = performance.now();
  });
  
  player.on('canplay', () => {
    if (playStartTime) {
      const loadTime = performance.now() - playStartTime;
      console.log(`[VideoJS Performance] Video ready to play in ${loadTime.toFixed(2)}ms`);
    }
  });
  
  // 监控缓冲事件
  player.on('waiting', () => {
    console.log('[VideoJS Performance] Buffering started');
  });
  
  player.on('canplaythrough', () => {
    console.log('[VideoJS Performance] Buffering completed');
  });
  
  // 监控错误
  player.on('error', (error) => {
    console.error('[VideoJS Performance] Player error:', error);
  });
};
