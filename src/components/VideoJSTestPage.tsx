import React, { useState, useRef } from 'react';
import {
  Box,
  Button,
  Stack,
  Text,
  Input,
  VStack,
  HStack,
  Badge,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Progress,
  Slider,
  SliderTrack,
  SliderFilledTrack,
  SliderThumb
} from '@chakra-ui/react';
import VideoJSPlayer, { VideoJSPlayerRef } from './VideoJSPlayer';
import { useVideoJSPlayer } from '../hooks/useVideoJSPlayer';

const VideoJSTestPage: React.FC = () => {
  const [videoPath, setVideoPath] = useState('');
  const [testResults, setTestResults] = useState<string[]>([]);
  const playerRef = useRef<VideoJSPlayerRef>(null);

  // 使用Video.js播放器状态管理
  const {
    state,
    actions,
    getVideoJSEventHandlers
  } = useVideoJSPlayer({
    onTimeUpdate: (currentTime, duration) => {
      // 实时更新，不需要添加到测试结果
    },
    onPlay: () => {
      addTestResult('✅ 播放事件触发');
    },
    onPause: () => {
      addTestResult('✅ 暂停事件触发');
    },
    onError: (error) => {
      addTestResult(`❌ 错误: ${error}`);
    },
    onReady: (player) => {
      addTestResult('✅ 播放器就绪');
    }
  });

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleLoadVideo = () => {
    if (!videoPath.trim()) {
      addTestResult('❌ 请输入视频路径');
      return;
    }
    addTestResult(`📁 加载视频: ${videoPath}`);
  };

  const runPerformanceTest = () => {
    addTestResult('🚀 开始性能测试...');
    
    // 测试快速跳转
    const testSeeks = [10, 30, 60, 120, 0];
    let seekIndex = 0;
    
    const performSeek = () => {
      if (seekIndex < testSeeks.length) {
        const time = testSeeks[seekIndex];
        actions.seek(time);
        addTestResult(`⏭️ 跳转到 ${time}s`);
        seekIndex++;
        setTimeout(performSeek, 1000);
      } else {
        addTestResult('✅ 性能测试完成');
      }
    };
    
    performSeek();
  };

  const testPlaybackControls = () => {
    addTestResult('🎮 测试播放控制...');
    
    // 测试播放/暂停循环
    let testCount = 0;
    const maxTests = 3;
    
    const testCycle = () => {
      if (testCount < maxTests) {
        if (state.isPlaying) {
          actions.pause();
          addTestResult(`⏸️ 暂停测试 ${testCount + 1}/${maxTests}`);
        } else {
          actions.play();
          addTestResult(`▶️ 播放测试 ${testCount + 1}/${maxTests}`);
        }
        testCount++;
        setTimeout(testCycle, 2000);
      } else {
        addTestResult('✅ 播放控制测试完成');
      }
    };
    
    testCycle();
  };

  const testVolumeControls = () => {
    addTestResult('🔊 测试音量控制...');
    
    const volumes = [0.5, 0.8, 0.2, 1.0];
    let volumeIndex = 0;
    
    const testVolume = () => {
      if (volumeIndex < volumes.length) {
        const volume = volumes[volumeIndex];
        actions.setVolume(volume);
        addTestResult(`🔊 设置音量: ${Math.round(volume * 100)}%`);
        volumeIndex++;
        setTimeout(testVolume, 1000);
      } else {
        // 测试静音
        actions.toggleMute();
        addTestResult('🔇 切换静音');
        setTimeout(() => {
          actions.toggleMute();
          addTestResult('🔊 取消静音');
          addTestResult('✅ 音量控制测试完成');
        }, 1000);
      }
    };
    
    testVolume();
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <Box p={6} maxWidth="1200px" mx="auto">
      <VStack spacing={6} align="stretch">
        <Text fontSize="2xl" fontWeight="bold" textAlign="center">
          Video.js 播放器测试页面
        </Text>

        {/* 视频路径输入 */}
        <Box>
          <Text mb={2} fontWeight="semibold">视频文件路径:</Text>
          <HStack>
            <Input
              placeholder="输入视频文件的完整路径，例如: /Users/<USER>/video.mp4"
              value={videoPath}
              onChange={(e) => setVideoPath(e.target.value)}
            />
            <Button onClick={handleLoadVideo} colorScheme="blue">
              加载视频
            </Button>
          </HStack>
        </Box>

        {/* 播放器容器 */}
        <Box
          height="400px"
          bg="black"
          borderRadius="lg"
          overflow="hidden"
          position="relative"
        >
          {videoPath ? (
            <VideoJSPlayer
              ref={playerRef}
              src={`file://${videoPath}`}
              width="100%"
              height="100%"
              fluid={true}
              responsive={true}
              controls={false}
              preload="metadata"
              {...getVideoJSEventHandlers()}
            />
          ) : (
            <Box
              height="100%"
              display="flex"
              alignItems="center"
              justifyContent="center"
              color="white"
              fontSize="lg"
            >
              请输入视频路径并点击加载视频
            </Box>
          )}
        </Box>

        {/* 播放器状态显示 */}
        <Box bg="gray.50" p={4} borderRadius="lg">
          <Text fontWeight="bold" mb={3}>播放器状态:</Text>
          <Stack spacing={2}>
            <HStack>
              <Badge colorScheme={state.isReady ? 'green' : 'red'}>
                {state.isReady ? '就绪' : '未就绪'}
              </Badge>
              <Badge colorScheme={state.isPlaying ? 'green' : 'gray'}>
                {state.isPlaying ? '播放中' : '暂停'}
              </Badge>
              <Badge colorScheme={state.isLoading ? 'yellow' : 'gray'}>
                {state.isLoading ? '加载中' : '已加载'}
              </Badge>
              <Badge colorScheme={state.hasError ? 'red' : 'green'}>
                {state.hasError ? '错误' : '正常'}
              </Badge>
            </HStack>
            
            <HStack>
              <Text>时间: {formatTime(state.currentTime)} / {formatTime(state.duration)}</Text>
              <Text>音量: {Math.round(state.volume * 100)}%</Text>
              <Text>静音: {state.isMuted ? '是' : '否'}</Text>
              <Text>播放速度: {state.playbackRate}x</Text>
            </HStack>

            {state.duration > 0 && (
              <Progress 
                value={(state.currentTime / state.duration) * 100} 
                colorScheme="blue"
                size="sm"
              />
            )}

            {state.hasError && (
              <Alert status="error" size="sm">
                <AlertIcon />
                <AlertTitle>播放器错误:</AlertTitle>
                <AlertDescription>{state.errorMessage}</AlertDescription>
              </Alert>
            )}
          </Stack>
        </Box>

        {/* 控制按钮 */}
        <HStack spacing={4} justify="center" wrap="wrap">
          <Button onClick={actions.togglePlay} colorScheme="blue">
            {state.isPlaying ? '暂停' : '播放'}
          </Button>
          <Button onClick={() => actions.seek(0)} variant="outline">
            回到开始
          </Button>
          <Button onClick={() => actions.skipTime(-10)} variant="outline">
            后退10s
          </Button>
          <Button onClick={() => actions.skipTime(10)} variant="outline">
            前进10s
          </Button>
          <Button onClick={actions.toggleMute} variant="outline">
            {state.isMuted ? '取消静音' : '静音'}
          </Button>
          <Button onClick={actions.toggleFullscreen} variant="outline">
            全屏
          </Button>
        </HStack>

        {/* 音量控制 */}
        <Box>
          <Text mb={2} fontWeight="semibold">音量控制:</Text>
          <Slider
            value={state.volume * 100}
            onChange={(value) => actions.setVolume(value / 100)}
            min={0}
            max={100}
            step={1}
          >
            <SliderTrack>
              <SliderFilledTrack />
            </SliderTrack>
            <SliderThumb />
          </Slider>
        </Box>

        {/* 测试按钮 */}
        <HStack spacing={4} justify="center" wrap="wrap">
          <Button onClick={runPerformanceTest} colorScheme="green">
            性能测试
          </Button>
          <Button onClick={testPlaybackControls} colorScheme="orange">
            播放控制测试
          </Button>
          <Button onClick={testVolumeControls} colorScheme="purple">
            音量控制测试
          </Button>
          <Button onClick={clearResults} variant="outline">
            清除结果
          </Button>
        </HStack>

        {/* 测试结果 */}
        <Box>
          <Text fontWeight="bold" mb={3}>测试结果:</Text>
          <Box
            bg="gray.900"
            color="white"
            p={4}
            borderRadius="lg"
            maxHeight="300px"
            overflowY="auto"
            fontFamily="mono"
            fontSize="sm"
          >
            {testResults.length === 0 ? (
              <Text color="gray.400">暂无测试结果</Text>
            ) : (
              testResults.map((result, index) => (
                <Text key={index} mb={1}>
                  {result}
                </Text>
              ))
            )}
          </Box>
        </Box>
      </VStack>
    </Box>
  );
};

export default VideoJSTestPage;
