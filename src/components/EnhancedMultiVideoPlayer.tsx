import {
  Box,
  Text,
  Grid
} from '@chakra-ui/react';
import { useState, useRef, useEffect, useCallback, useMemo, forwardRef, useImperativeHandle } from 'react';
import { VideoFile, CameraAngle } from '../types/electron';
import { useKeyboardFrameControl } from '../hooks/useKeyboardFrameControl';
import { toaster } from '../ui/toaster';
import VideoPlayerControls from './VideoPlayerControls';
import VideoJSPlayer, { VideoJSPlayerRef } from './VideoJSPlayer';
import { useVideoJSPlayer } from '../hooks/useVideoJSPlayer';
import { createLogger } from '../utils/logger';

// 创建日志实例
const logger = createLogger('EnhancedMultiVideoPlayer');

export interface EnhancedMultiVideoPlayerRef {
  seek: (time: number) => void;
  volumeChange: (volume: number) => void;
  toggleMute: () => void;
  skipTime: (seconds: number) => void;
  play: () => void;
  pause: () => void;
  togglePlay: () => void;
  toggleFullscreen: () => void;
  // 缩放控制
  toggleZoom?: () => void;
  setScale?: (scale: number) => void;
  resetZoom?: () => void;
  // 视频显示控制
  toggleVideoVisibility?: (index: number) => void;
  returnToMultiVideoLayout: () => void;
  // 获取当前视频顺序
  getVideoOrder?: () => VideoFile[];
}

interface EnhancedMultiVideoPlayerProps {
  videoFiles: VideoFile[];
  onTimeUpdate?: (currentTime: number, duration: number) => void;
  onClose?: () => void;
  onSingleVideoPlay?: (file: VideoFile) => void;
  // 控制函数
  onPlay?: () => void;
  onPause?: () => void;
  onTogglePlay?: () => void;
  onSeek?: (time: number) => void;
  onVolumeChange?: (volume: number) => void;
  onToggleMute?: () => void;
  onSkipTime?: (seconds: number) => void;
  // 状态
  isPlaying?: boolean;
  masterTime?: number;
  masterDuration?: number;
  volume?: number;
  isMuted?: boolean;
  // 单视频模式标识
  isSingleVideoMode?: boolean;
  // 返回多视频模式
  showReturnToMultiVideo?: boolean;
  onReturnToMultiVideo?: () => void;
  // 主视频独占模式状态变化回调
  onMainVideoFocusChange?: (isFocused: boolean) => void;
  // 显示视频数量变化回调
  onVisibleVideoCountChange?: (count: number) => void;
  // 缩放状态变化回调
  onZoomStateChange?: (isZoomed: boolean, scale: number) => void;
  // 视频可见性控制
  videoVisibility?: boolean[];
  onVideoVisibilityChange?: (visibility: boolean[]) => void;
  // 剪辑状态
  isClipping?: boolean;
  clipProgress?: number;
}

const EnhancedMultiVideoPlayer = forwardRef<EnhancedMultiVideoPlayerRef, EnhancedMultiVideoPlayerProps>(({
  videoFiles,
  onTimeUpdate,
  onSingleVideoPlay,
  onPlay,
  onPause,
  onTogglePlay,
  onSeek,
  onVolumeChange,
  onToggleMute,
  onSkipTime,
  isPlaying: externalIsPlaying,
  masterTime: externalMasterTime,
  masterDuration: externalMasterDuration,
  volume: externalVolume,
  isMuted: externalIsMuted,
  isSingleVideoMode = false,
  showReturnToMultiVideo = false,
  onReturnToMultiVideo,
  onMainVideoFocusChange,
  onVisibleVideoCountChange,
  onZoomStateChange,
  videoVisibility: externalVideoVisibility,
  onVideoVisibilityChange,
  isClipping = false,
  clipProgress = 0
}: EnhancedMultiVideoPlayerProps, ref) => {
  
  // 状态管理
  const [displayFiles, setDisplayFiles] = useState<VideoFile[]>([]);
  const [videoVisibility, setVideoVisibility] = useState<boolean[]>([]);
  const [isPlaying, setIsPlaying] = useState(false);
  const [masterTime, setMasterTime] = useState(0);
  const [masterDuration, setMasterDuration] = useState(0);
  const [currentVolume, setCurrentVolume] = useState(1);
  const [currentIsMuted, setCurrentIsMuted] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showControls, setShowControls] = useState(true);
  const [scale, setScale] = useState(1);
  const [showZoomControls, setShowZoomControls] = useState(false);
  const [isMainVideoFocused, setIsMainVideoFocused] = useState(false);
  const [isClipMode, setIsClipMode] = useState(false);
  const [clipStartTime, setClipStartTime] = useState(0);
  const [clipEndTime, setClipEndTime] = useState(0);

  // Video.js 播放器引用
  const videoJSRefs = useRef<(VideoJSPlayerRef | null)[]>([]);
  
  // 主播放器状态管理
  const {
    state: playerState,
    actions: playerActions,
    getVideoJSEventHandlers
  } = useVideoJSPlayer({
    onTimeUpdate: (currentTime, duration) => {
      setMasterTime(currentTime);
      setMasterDuration(duration);
      onTimeUpdate?.(currentTime, duration);
    },
    onPlay: () => {
      setIsPlaying(true);
      onPlay?.();
    },
    onPause: () => {
      setIsPlaying(false);
      onPause?.();
    },
    onVolumeChange: (volume, muted) => {
      setCurrentVolume(volume);
      setCurrentIsMuted(muted);
      onVolumeChange?.(volume);
    }
  });

  // 初始化显示文件和可见性
  useEffect(() => {
    if (videoFiles.length > 0) {
      setDisplayFiles(videoFiles);
      
      // 使用外部传入的可见性状态，或默认全部可见
      const initialVisibility = externalVideoVisibility || new Array(videoFiles.length).fill(true);
      setVideoVisibility(initialVisibility);
      
      // 重置播放器引用数组
      videoJSRefs.current = new Array(videoFiles.length).fill(null);
    }
  }, [videoFiles, externalVideoVisibility]);

  // 同步外部状态
  useEffect(() => {
    if (typeof externalIsPlaying === 'boolean') {
      setIsPlaying(externalIsPlaying);
    }
  }, [externalIsPlaying]);

  useEffect(() => {
    if (typeof externalMasterTime === 'number') {
      setMasterTime(externalMasterTime);
    }
  }, [externalMasterTime]);

  useEffect(() => {
    if (typeof externalMasterDuration === 'number') {
      setMasterDuration(externalMasterDuration);
    }
  }, [externalMasterDuration]);

  useEffect(() => {
    if (typeof externalVolume === 'number') {
      setCurrentVolume(externalVolume);
    }
  }, [externalVolume]);

  useEffect(() => {
    if (typeof externalIsMuted === 'boolean') {
      setCurrentIsMuted(externalIsMuted);
    }
  }, [externalIsMuted]);

  // 同步播放控制到所有播放器
  const syncPlay = useCallback(async () => {
    const visiblePlayers = videoJSRefs.current.filter((ref, index) => 
      ref && videoVisibility[index]
    );
    
    try {
      await Promise.all(visiblePlayers.map(player => player?.play()));
      setIsPlaying(true);
    } catch (error) {
      console.error('Sync play failed:', error);
    }
  }, [videoVisibility]);

  const syncPause = useCallback(() => {
    const visiblePlayers = videoJSRefs.current.filter((ref, index) => 
      ref && videoVisibility[index]
    );
    
    visiblePlayers.forEach(player => player?.pause());
    setIsPlaying(false);
  }, [videoVisibility]);

  const syncSeek = useCallback((time: number) => {
    const visiblePlayers = videoJSRefs.current.filter((ref, index) => 
      ref && videoVisibility[index]
    );
    
    visiblePlayers.forEach(player => player?.seek(time));
    setMasterTime(time);
    onSeek?.(time);
  }, [videoVisibility, onSeek]);

  const handleVolumeChange = useCallback((volume: number) => {
    const visiblePlayers = videoJSRefs.current.filter((ref, index) => 
      ref && videoVisibility[index]
    );
    
    visiblePlayers.forEach(player => player?.setVolume(volume));
    setCurrentVolume(volume);
    onVolumeChange?.(volume);
  }, [videoVisibility, onVolumeChange]);

  const handleToggleMute = useCallback(() => {
    const newMuted = !currentIsMuted;
    const visiblePlayers = videoJSRefs.current.filter((ref, index) => 
      ref && videoVisibility[index]
    );
    
    visiblePlayers.forEach(player => player?.setMuted(newMuted));
    setCurrentIsMuted(newMuted);
  }, [currentIsMuted, videoVisibility]);

  const handleSkipTime = useCallback((seconds: number) => {
    const newTime = Math.max(0, Math.min(masterDuration, masterTime + seconds));
    syncSeek(newTime);
  }, [masterTime, masterDuration, syncSeek]);

  const togglePlay = useCallback(() => {
    if (isPlaying) {
      syncPause();
    } else {
      syncPlay();
    }
  }, [isPlaying, syncPlay, syncPause]);

  const toggleFullscreen = useCallback(() => {
    // 这里可以实现全屏逻辑
    // 由于Video.js的全屏API，我们可能需要特殊处理
    const firstVisiblePlayer = videoJSRefs.current.find((ref, index) => 
      ref && videoVisibility[index]
    );
    
    if (firstVisiblePlayer?.player) {
      if (firstVisiblePlayer.player.isFullscreen()) {
        firstVisiblePlayer.player.exitFullscreen();
      } else {
        firstVisiblePlayer.player.requestFullscreen();
      }
    }
  }, [videoVisibility]);

  // 计算可见视频数量
  const visibleVideoCount = useMemo(() => {
    return videoVisibility.filter(visible => visible).length;
  }, [videoVisibility]);

  // 通知可见视频数量变化
  useEffect(() => {
    onVisibleVideoCountChange?.(visibleVideoCount);
  }, [visibleVideoCount, onVisibleVideoCountChange]);

  // 暴露控制方法给外部
  useImperativeHandle(ref, () => ({
    seek: syncSeek,
    volumeChange: handleVolumeChange,
    toggleMute: handleToggleMute,
    skipTime: handleSkipTime,
    play: syncPlay,
    pause: syncPause,
    togglePlay,
    toggleFullscreen,
    // 缩放控制 - 这里可以根据需要实现
    toggleZoom: () => {
      // 实现缩放逻辑
    },
    setScale: (newScale: number) => {
      setScale(newScale);
    },
    resetZoom: () => {
      setScale(1);
      setShowZoomControls(false);
    },
    // 视频显示控制
    toggleVideoVisibility: (index: number) => {
      const newVisibility = [...videoVisibility];
      newVisibility[index] = !newVisibility[index];
      setVideoVisibility(newVisibility);
      onVideoVisibilityChange?.(newVisibility);
    },
    returnToMultiVideoLayout: () => {
      setIsMainVideoFocused(false);
      onMainVideoFocusChange?.(false);
    },
    // 获取当前视频顺序
    getVideoOrder: () => displayFiles
  }), [syncSeek, handleVolumeChange, handleToggleMute, handleSkipTime, syncPlay, syncPause, togglePlay, toggleFullscreen, videoVisibility, displayFiles, onVideoVisibilityChange, onMainVideoFocusChange]);

  return (
    <Box
      position="relative"
      width="100%"
      height="100%"
      bg="black"
      overflow="hidden"
    >
      {/* 视频网格容器 */}
      <Grid
        templateColumns={visibleVideoCount === 1 ? '1fr' : visibleVideoCount === 2 ? '1fr 1fr' : '1fr 1fr'}
        templateRows={visibleVideoCount <= 2 ? '1fr' : '1fr 1fr'}
        gap={1}
        width="100%"
        height="100%"
      >
        {displayFiles.map((file, index) => {
          const isVideoVisible = videoVisibility[index];
          const videoUrl = `file://${file.path}`;
          
          if (!isVideoVisible) return null;
          
          return (
            <Box
              key={file.id}
              height="100%"
              width="100%"
              overflow="hidden"
              alignItems="center"
              justifyContent="center"
              display="flex"
              position="relative"
            >
              <VideoJSPlayer
                ref={(el) => {
                  videoJSRefs.current[index] = el;
                }}
                src={videoUrl}
                width="100%"
                height="100%"
                fluid={true}
                responsive={true}
                controls={false}
                preload="metadata"
                muted={currentIsMuted}
                {...getVideoJSEventHandlers()}
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'contain'
                }}
              />
              
              {/* 视频标签 */}
              <Box
                position="absolute"
                top={2}
                left={2}
                bg="rgba(0,0,0,0.7)"
                color="white"
                px={2}
                py={1}
                borderRadius="md"
                fontSize="sm"
                zIndex={10}
              >
                {file.name}
              </Box>
            </Box>
          );
        })}
      </Grid>
    </Box>
  );
});

EnhancedMultiVideoPlayer.displayName = 'EnhancedMultiVideoPlayer';

export default EnhancedMultiVideoPlayer;
