import React, { useRef, useEffect, useCallback, forwardRef, useImperativeHandle } from 'react';
import videojs from 'video.js';
import 'video.js/dist/video-js.css';

// 导入优化配置
import { getOptimizedConfig, createPerformanceMonitor } from '../config/videojs-config';

export interface VideoJSPlayerProps {
  src?: string;
  poster?: string;
  width?: number | string;
  height?: number | string;
  fluid?: boolean;
  responsive?: boolean;
  controls?: boolean;
  autoplay?: boolean;
  preload?: 'auto' | 'metadata' | 'none';
  muted?: boolean;
  loop?: boolean;
  playsinline?: boolean;

  // 性能优化选项
  scenario?: 'single' | 'multi';
  enablePerformanceMonitoring?: boolean;
  
  // 事件回调
  onReady?: (player: videojs.Player) => void;
  onPlay?: () => void;
  onPause?: () => void;
  onTimeUpdate?: (currentTime: number, duration: number) => void;
  onSeeking?: () => void;
  onSeeked?: () => void;
  onLoadedMetadata?: () => void;
  onLoadStart?: () => void;
  onCanPlay?: () => void;
  onError?: (error: any) => void;
  onVolumeChange?: (volume: number, muted: boolean) => void;
  onDurationChange?: (duration: number) => void;
  
  // Video.js 特定选项
  options?: videojs.PlayerOptions;
  
  // 样式
  className?: string;
  style?: React.CSSProperties;
}

export interface VideoJSPlayerRef {
  player: videojs.Player | null;
  play: () => Promise<void>;
  pause: () => void;
  seek: (time: number) => void;
  setVolume: (volume: number) => void;
  setMuted: (muted: boolean) => void;
  getCurrentTime: () => number;
  getDuration: () => number;
  getVolume: () => number;
  isMuted: () => boolean;
  isPaused: () => boolean;
  dispose: () => void;
}

const VideoJSPlayer = forwardRef<VideoJSPlayerRef, VideoJSPlayerProps>(({
  src,
  poster,
  width = '100%',
  height = '100%',
  fluid = true,
  responsive = true,
  controls = false, // 默认关闭内置控制栏，使用自定义控制栏
  autoplay = false,
  preload = 'metadata',
  muted = false,
  loop = false,
  playsinline = true,

  // 性能优化选项
  scenario = 'single',
  enablePerformanceMonitoring = process.env.NODE_ENV === 'development',
  
  onReady,
  onPlay,
  onPause,
  onTimeUpdate,
  onSeeking,
  onSeeked,
  onLoadedMetadata,
  onLoadStart,
  onCanPlay,
  onError,
  onVolumeChange,
  onDurationChange,
  
  options = {},
  className = '',
  style = {}
}, ref) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const playerRef = useRef<videojs.Player | null>(null);

  // 初始化播放器
  useEffect(() => {
    if (!videoRef.current) return;

    const videoElement = videoRef.current;
    
    // 使用简化的配置，避免插件问题
    const playerOptions: videojs.PlayerOptions = {
      controls,
      fluid,
      responsive,
      autoplay,
      preload,
      muted,
      loop,
      playsinline,
      width: typeof width === 'number' ? width : undefined,
      height: typeof height === 'number' ? height : undefined,
      poster,
      sources: src ? [{ src, type: 'video/mp4' }] : undefined,

      // 基础HTML5优化
      html5: {
        // 启用原生轨道支持
        nativeTextTracks: true,
        nativeAudioTracks: true,
        nativeVideoTracks: true,
      },

      // 技术栈优先级
      techOrder: ['html5'],

      // 语言配置
      language: 'zh-CN',

      // 用户活动检测
      inactivityTimeout: scenario === 'multi' ? 2000 : 3000,

      // 错误处理
      suppressNotSupportedError: true,

      // 合并用户自定义选项
      ...options
    };

    // 创建播放器实例
    const player = videojs(videoElement, playerOptions);
    playerRef.current = player;

    // 播放器就绪回调
    player.ready(() => {
      console.log('VideoJS Player is ready');

      // 启用性能监控
      if (enablePerformanceMonitoring) {
        createPerformanceMonitor(player);
      }

      onReady?.(player);
    });

    return () => {
      if (playerRef.current) {
        playerRef.current.dispose();
        playerRef.current = null;
      }
    };
  }, []);

  // 设置事件监听器
  useEffect(() => {
    const player = playerRef.current;
    if (!player) return;

    const handlePlay = () => onPlay?.();
    const handlePause = () => onPause?.();
    const handleTimeUpdate = () => {
      const currentTime = player.currentTime() || 0;
      const duration = player.duration() || 0;
      onTimeUpdate?.(currentTime, duration);
    };
    const handleSeeking = () => onSeeking?.();
    const handleSeeked = () => onSeeked?.();
    const handleLoadedMetadata = () => onLoadedMetadata?.();
    const handleLoadStart = () => onLoadStart?.();
    const handleCanPlay = () => onCanPlay?.();
    const handleError = (error: any) => onError?.(error);
    const handleVolumeChange = () => {
      const volume = player.volume() || 0;
      const muted = player.muted() || false;
      onVolumeChange?.(volume, muted);
    };
    const handleDurationChange = () => {
      const duration = player.duration() || 0;
      onDurationChange?.(duration);
    };

    // 添加事件监听器
    player.on('play', handlePlay);
    player.on('pause', handlePause);
    player.on('timeupdate', handleTimeUpdate);
    player.on('seeking', handleSeeking);
    player.on('seeked', handleSeeked);
    player.on('loadedmetadata', handleLoadedMetadata);
    player.on('loadstart', handleLoadStart);
    player.on('canplay', handleCanPlay);
    player.on('error', handleError);
    player.on('volumechange', handleVolumeChange);
    player.on('durationchange', handleDurationChange);

    return () => {
      // 移除事件监听器
      player.off('play', handlePlay);
      player.off('pause', handlePause);
      player.off('timeupdate', handleTimeUpdate);
      player.off('seeking', handleSeeking);
      player.off('seeked', handleSeeked);
      player.off('loadedmetadata', handleLoadedMetadata);
      player.off('loadstart', handleLoadStart);
      player.off('canplay', handleCanPlay);
      player.off('error', handleError);
      player.off('volumechange', handleVolumeChange);
      player.off('durationchange', handleDurationChange);
    };
  }, [onPlay, onPause, onTimeUpdate, onSeeking, onSeeked, onLoadedMetadata, onLoadStart, onCanPlay, onError, onVolumeChange, onDurationChange]);

  // 更新视频源
  useEffect(() => {
    const player = playerRef.current;
    if (!player || !src) return;

    // 检查当前源是否与新源不同
    const currentSrc = player.currentSrc();
    if (currentSrc !== src) {
      player.src({ src, type: 'video/mp4' });
    }
  }, [src]);

  // 暴露播放器控制方法
  useImperativeHandle(ref, () => ({
    player: playerRef.current,

    play: async () => {
      if (playerRef.current) {
        try {
          await playerRef.current.play();
        } catch (error) {
          console.error('Play failed:', error);
          throw error;
        }
      }
    },

    pause: () => {
      if (playerRef.current) {
        playerRef.current.pause();
      }
    },

    seek: (time: number) => {
      if (playerRef.current) {
        playerRef.current.currentTime(time);
      }
    },

    setVolume: (volume: number) => {
      if (playerRef.current) {
        playerRef.current.volume(Math.max(0, Math.min(1, volume)));
      }
    },

    setMuted: (muted: boolean) => {
      if (playerRef.current) {
        playerRef.current.muted(muted);
      }
    },

    getCurrentTime: () => {
      return playerRef.current?.currentTime() || 0;
    },

    getDuration: () => {
      return playerRef.current?.duration() || 0;
    },

    getVolume: () => {
      return playerRef.current?.volume() || 0;
    },

    isMuted: () => {
      return playerRef.current?.muted() || false;
    },

    isPaused: () => {
      return playerRef.current?.paused() || true;
    },

    dispose: () => {
      if (playerRef.current) {
        playerRef.current.dispose();
        playerRef.current = null;
      }
    }
  }), []);

  return (
    <div
      className={`videojs-player-wrapper ${className}`}
      style={{
        width: typeof width === 'string' ? width : `${width}px`,
        height: typeof height === 'string' ? height : `${height}px`,
        ...style
      }}
    >
      <video
        ref={videoRef}
        className="video-js vjs-default-skin"
        data-setup="{}"
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'contain'
        }}
      />
    </div>
  );
});

VideoJSPlayer.displayName = 'VideoJSPlayer';

export default VideoJSPlayer;
