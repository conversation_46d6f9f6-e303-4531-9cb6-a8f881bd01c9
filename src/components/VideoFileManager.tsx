import { <PERSON>, But<PERSON>, Stack, Text, Input, Spinner, Badge } from '@chakra-ui/react';
import { <PERSON><PERSON>older, FiSearch, FiVideo, FiCalendar, FiClock, FiChevronDown, FiChevronRight, FiPlay, FiChevronLeft } from 'react-icons/fi';
import { useState, useCallback, useMemo, useEffect } from 'react';
import { VideoFile } from '../types/electron';
import { useFileMediaContent } from '../hooks/useFileMediaContent';
import MediaContentButton from './MediaContentButton';
import MediaContentViewer from './MediaContentViewer';

type GroupType = 'none' | 'date' | 'time';
type CameraType = 'all' | 'F' | 'R' | 'I';

interface SubGroup {
  key: string;
  label: string;
  files: VideoFile[];
  isExpanded: boolean;
}

interface FileGroup {
  key: string;
  label: string;
  files: VideoFile[];
  isExpanded: boolean;
  subGroups?: SubGroup[]; // 二级分组，仅在按时间分组时使用
}

interface VideoFileManagerProps {
  onFileSelect?: (file: VideoFile, options?: { seekTime?: number; shouldPlay?: boolean; }) => void;
  onFilesUpdate?: (files: VideoFile[]) => void;
  onMultiVideoPlay?: (files: VideoFile[]) => void; // 多视频播放回调
  isCollapsed?: boolean; // 是否折叠
  onToggleCollapse?: () => void; // 切换折叠状态的回调
  onVideoJSTest?: () => void; // Video.js 测试模式回调
}

const VideoFileManager = ({ onFileSelect, onFilesUpdate, onMultiVideoPlay, isCollapsed = false, onToggleCollapse, onVideoJSTest }: VideoFileManagerProps) => {
  const [selectedFolder, setSelectedFolder] = useState<string>('');
  const [videoFiles, setVideoFiles] = useState<VideoFile[]>([]);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [groupType, setGroupType] = useState<GroupType>('none');
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set());
  const [selectedCameraType, setSelectedCameraType] = useState<CameraType>('all');

  // 媒体内容管理
  const {
    getScreenshotsForFile,
    getClipsForFile,
    getGroupsForFile,
    removeScreenshot,
    removeClip,
    removeGroup
  } = useFileMediaContent();

  // 媒体内容查看器状态
  const [mediaViewerOpen, setMediaViewerOpen] = useState(false);
  const [selectedVideoForMedia, setSelectedVideoForMedia] = useState<VideoFile | null>(null);

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  // 格式化时间戳
  const formatTimestamp = (timestamp: string): string => {
    const date = new Date(timestamp);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  // 获取摄像头位置标识和颜色
  const getCameraPosition = (filename: string): { label: string; colorScheme: string; bgColor: string; textColor: string } => {
    const lastChar = filename.charAt(filename.lastIndexOf('.') - 1).toUpperCase();
    switch (lastChar) {
      case 'F': return {
        label: '前',
        colorScheme: 'teal',
        bgColor: '#0d9488', // 深青色 - 前置摄像头，与主题色呼应
        textColor: 'white'
      };
      case 'R': return {
        label: '后',
        colorScheme: 'orange',
        bgColor: '#ea580c', // 橙色 - 后置摄像头，暖色调
        textColor: 'white'
      };
      case 'I': return {
        label: '内',
        colorScheme: 'gray',
        bgColor: '#6b7280', // 灰色 - 内置摄像头，中性色调
        textColor: 'white'
      };
      default: return {
        label: '',
        colorScheme: 'gray',
        bgColor: '#6b7280',
        textColor: 'white'
      };
    }
  };

  // 获取文件的摄像头类型
  const getFilesCameraType = (filename: string): string => {
    const lastChar = filename.charAt(filename.lastIndexOf('.') - 1).toUpperCase();
    return ['F', 'R', 'I'].includes(lastChar) ? lastChar : '';
  };

  // 获取可用的摄像头类型
  const availableCameraTypes = useMemo(() => {
    const types = new Set<string>();
    videoFiles.forEach(file => {
      const cameraType = getFilesCameraType(file.name);
      if (cameraType) {
        types.add(cameraType);
      }
    });
    return Array.from(types).sort();
  }, [videoFiles]);

  // 根据摄像头类型过滤文件
  const filteredVideoFiles = useMemo(() => {
    if (selectedCameraType === 'all') {
      return videoFiles;
    }
    return videoFiles.filter(file => getFilesCameraType(file.name) === selectedCameraType);
  }, [videoFiles, selectedCameraType]);

  // 从文件名中提取时间信息
  const extractTimeFromFilename = useCallback((filename: string) => {
    // 匹配格式：2025_0706_194728 (年_月日_时分秒)
    const timeMatch = filename.match(/(\d{4})_(\d{4})_(\d{6})/);
    if (timeMatch) {
      const [, year, monthDay, timeStr] = timeMatch;
      const month = monthDay.substring(0, 2);
      const day = monthDay.substring(2, 4);
      const hour = timeStr.substring(0, 2);
      const minute = timeStr.substring(2, 4);
      const second = timeStr.substring(4, 6);

      return {
        year,
        month,
        day,
        hour,
        minute,
        second,
        timeKey: `${hour}:${minute}:${second}` // 时:分:秒格式
      };
    }
    return null;
  }, []);

  // 分组文件
  const groupedFiles = useMemo(() => {
    const filtered = filteredVideoFiles.filter(file =>
      file.name.toLowerCase().includes(searchQuery.toLowerCase())
    );

    if (groupType === 'none') {
      // 不分组，返回单个组
      return [{
        key: 'all',
        label: '所有文件',
        files: filtered.sort((a, b) => a.name.localeCompare(b.name)),
        isExpanded: true
      }];
    }

    if (groupType === 'date') {
      // 按日期分组 (YYYY-MM-DD)
      const groups: { [key: string]: VideoFile[] } = {};

      filtered.forEach(file => {
        const date = new Date(file.timestamp);
        const groupKey = date.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        });

        if (!groups[groupKey]) {
          groups[groupKey] = [];
        }
        groups[groupKey].push(file);
      });

      // 转换为 FileGroup 数组并排序
      const fileGroups: FileGroup[] = Object.entries(groups)
        .map(([key, files]) => ({
          key,
          label: key,
          files: files.sort((a, b) => a.name.localeCompare(b.name)),
          isExpanded: expandedGroups.has(key)
        }))
        .sort((a, b) => b.key.localeCompare(a.key)); // 最新的在前

      return fileGroups;
    } else {
      // 按时间分组 (按小时，然后二级分组按时间戳)
      const hourGroups: { [key: string]: VideoFile[] } = {};

      filtered.forEach(file => {
        const date = new Date(file.timestamp);
        const hourKey = date.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        }) + ' ' + date.getHours().toString().padStart(2, '0') + ':00';

        if (!hourGroups[hourKey]) {
          hourGroups[hourKey] = [];
        }
        hourGroups[hourKey].push(file);
      });

      // 为每个小时组创建二级分组
      const fileGroups: FileGroup[] = Object.entries(hourGroups)
        .map(([hourKey, hourFiles]) => {
          // 按文件名中的时分秒进行二级分组
          const subGroups: { [key: string]: VideoFile[] } = {};

          hourFiles.forEach(file => {
            const timeInfo = extractTimeFromFilename(file.name);
            let subGroupKey: string;

            if (timeInfo) {
              // 使用文件名中的时分秒信息
              subGroupKey = timeInfo.timeKey; // 格式：HH:MM:SS
            } else {
              // 如果无法从文件名提取时间，则使用时间戳
              const date = new Date(file.timestamp);
              subGroupKey = `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`;
            }

            if (!subGroups[subGroupKey]) {
              subGroups[subGroupKey] = [];
            }
            subGroups[subGroupKey].push(file);
          });

          // 转换为 SubGroup 数组
          const subGroupArray: SubGroup[] = Object.entries(subGroups)
            .map(([subKey, subFiles]) => ({
              key: `${hourKey}-${subKey}`,
              label: subKey,
              files: subFiles.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()),
              isExpanded: expandedGroups.has(`${hourKey}-${subKey}`)
            }))
            .sort((a, b) => a.label.localeCompare(b.label));

          return {
            key: hourKey,
            label: hourKey,
            files: hourFiles.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()),
            isExpanded: expandedGroups.has(hourKey),
            subGroups: subGroupArray
          };
        })
        .sort((a, b) => b.key.localeCompare(a.key)); // 最新的在前

      return fileGroups;
    }
  }, [filteredVideoFiles, searchQuery, groupType, expandedGroups, extractTimeFromFilename]);

  // 切换分组展开/收起
  const toggleGroup = useCallback((groupKey: string) => {
    setExpandedGroups(prev => {
      const newSet = new Set(prev);
      if (newSet.has(groupKey)) {
        newSet.delete(groupKey);
      } else {
        newSet.add(groupKey);
      }
      return newSet;
    });
  }, []);

  // 获取摄像头角度优先级
  const getCameraAnglePriority = useCallback((filename: string): number => {
    const lastChar = filename.charAt(filename.lastIndexOf('.') - 1).toUpperCase();
    switch (lastChar) {
      case 'F': return 1; // 前 - 最高优先级
      case 'R': return 2; // 后 - 中等优先级
      case 'I': return 3; // 内 - 最低优先级
      default: return 4; // 未知 - 最低优先级
    }
  }, []);

  // 处理分组播放
  const handleGroupPlay = useCallback((files: VideoFile[]) => {
    if (files.length === 0) return;

    if (files.length === 1) {
      // 单个视频：直接使用默认播放器
      onFileSelect?.(files[0]);
      return;
    }

    if (files.length === 2) {
      // 两个视频：按照前、后、内的优先顺序选择主视频
      const sortedFiles = [...files].sort((a, b) => {
        const priorityA = getCameraAnglePriority(a.name);
        const priorityB = getCameraAnglePriority(b.name);
        return priorityA - priorityB;
      });

      // 选择优先级最高的视频作为主视频和GPS源
      const mainVideo = sortedFiles[0];

      // 先选择主视频进行GPS数据提取
      onFileSelect?.(mainVideo);

      // 然后启动多视频播放
      onMultiVideoPlay?.(sortedFiles);
      return;
    }

    // 三个或更多视频：使用原有逻辑，最多选择3个
    const selectedFiles = files.slice(0, 3);

    // 按照前、后、内的优先顺序排序，选择优先级最高的作为GPS源
    const sortedFiles = [...selectedFiles].sort((a, b) => {
      const priorityA = getCameraAnglePriority(a.name);
      const priorityB = getCameraAnglePriority(b.name);
      return priorityA - priorityB;
    });

    const gpsSourceFile = sortedFiles[0]; // 选择优先级最高的作为GPS源

    // 先选择GPS源文件进行GPS数据提取
    onFileSelect?.(gpsSourceFile);

    // 然后启动多视频播放，保持原有的文件顺序
    onMultiVideoPlay?.(selectedFiles);
  }, [onMultiVideoPlay, onFileSelect, getCameraAnglePriority]);

  // 媒体内容操作处理函数
  const handleOpenScreenshot = useCallback(async (imagePath: string) => {
    try {
      await (window as any).electronAPI?.showItemInFolder?.(imagePath);
    } catch (error) {
      console.error('打开截图失败:', error);
    }
  }, []);

  const handlePlayClip = useCallback(async (clipPath: string) => {
    try {
      // 这里可以实现播放剪辑的逻辑，比如在默认播放器中打开
      await (window as any).electronAPI?.openExternal?.(clipPath);
    } catch (error) {
      console.error('播放剪辑失败:', error);
    }
  }, []);

  const handleOpenInFolder = useCallback(async (filePath: string) => {
    try {
      await (window as any).electronAPI?.showItemInFolder?.(filePath);
    } catch (error) {
      console.error('在文件夹中显示失败:', error);
    }
  }, []);

  // 打开媒体内容查看器
  const handleOpenMediaViewer = useCallback((file: VideoFile) => {
    setSelectedVideoForMedia(file);
    setMediaViewerOpen(true);
  }, []);

  // 关闭媒体内容查看器
  const handleCloseMediaViewer = useCallback(() => {
    setMediaViewerOpen(false);
    setSelectedVideoForMedia(null);
  }, []);

  // 加载指定文件夹的视频文件
  const loadVideoFolder = useCallback(async (folderPath: string) => {
    try {
      setIsLoading(true);

      // 尝试扫描视频文件来检查文件夹是否存在
      try {
        const files = await window.electronAPI.scanVideoFiles(folderPath);

        setSelectedFolder(folderPath);
        setVideoFiles(files);
        onFilesUpdate?.(files);

        // 默认展开第一个分组
        if (files.length > 0) {
          const firstDate = new Date(files[0].timestamp);
          const firstGroupKey = groupType === 'date'
            ? firstDate.toLocaleDateString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit' })
            : firstDate.toLocaleDateString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit' }) + ' ' + firstDate.getHours().toString().padStart(2, '0') + ':00';
          setExpandedGroups(new Set([firstGroupKey]));
        }

        return true;
      } catch (scanError) {

        // 清空配置中的文件夹路径
        await window.electronAPI.setLastVideoFolder(null);
        setSelectedFolder('');
        setVideoFiles([]);
        onFilesUpdate?.([]);
        return false;
      }
    } catch (error) {
      console.error('加载文件夹时出错:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [groupType, onFilesUpdate]);

  // 选择文件夹并扫描视频文件
  const handleSelectFolder = useCallback(async () => {
    try {
      setIsLoading(true);

      // 选择文件夹
      const folderPath = await window.electronAPI.selectFolder();
      if (!folderPath) {
        setIsLoading(false);
        return;
      }

      // 加载文件夹
      const success = await loadVideoFolder(folderPath);

      // 如果加载成功，保存到配置
      if (success) {
        await window.electronAPI.setLastVideoFolder(folderPath);
      }

    } catch (error) {
      console.error('选择文件夹或扫描文件时出错:', error);
    } finally {
      setIsLoading(false);
    }
  }, [loadVideoFolder]);

  // 组件初始化时加载上次使用的文件夹
  useEffect(() => {
    const loadLastFolder = async () => {
      try {
        const lastFolder = await window.electronAPI.getLastVideoFolder();
        if (lastFolder) {

          await loadVideoFolder(lastFolder);
        }
      } catch (error) {
        console.error('加载上次使用的文件夹失败:', error);
      }
    };

    loadLastFolder();
  }, [loadVideoFolder]);

  // 处理文件点击
  const handleFileClick = useCallback((file: VideoFile) => {
    onFileSelect?.(file);
  }, [onFileSelect]);

  // 计算总文件数
  const totalFiles = groupedFiles.reduce((sum, group) => sum + group.files.length, 0);

  return (
    <Box
      width={isCollapsed ? "48px" : "300px"}
      bg="gray.100"
      borderRight="1px solid"
      borderColor="gray.200"
      display="flex"
      flexDirection="column"
      height="100vh"
      transition="width 0.3s ease"
      position="relative"
    >
      {/* 折叠/展开按钮 */}
      <Box
        position="absolute"
        top="50%"
        right="-16px"
        transform="translateY(-50%)"
        zIndex={10}
      >
        <Box
          display="flex"
          alignItems="center"
          justifyContent="center"
          bg="gray.100"
          borderRadius="full"
          border="1px solid"
          borderColor="gray.200"
          boxShadow="sm"
          cursor="pointer"
          onClick={onToggleCollapse}
          _hover={{
            bg: "gray.200",
            boxShadow: "md"
          }}
          transition="all 0.2s ease"
          width="32px"
          height="32px"
        >
          {/* 主要图标 */}
          <Box
            color="gray.600"
            display="flex"
            alignItems="center"
            justifyContent="center"
          >
            <FiChevronLeft
              size={16}
              style={{
                transform: isCollapsed ? 'rotate(180deg)' : 'rotate(0deg)',
                transition: 'transform 0.2s ease'
              }}
            />
          </Box>
        </Box>
      </Box>

      {!isCollapsed && (
        <>
          {/* 文件夹选择按钮 */}
          <Box p={4} borderBottom="1px solid" borderColor="gray.200" flexShrink={0}>
            <Stack spacing={2}>
              <Button
                colorScheme="teal"
                size="sm"
                width="full"
                onClick={handleSelectFolder}
              >
                <FiFolder />
                选择视频文件夹
              </Button>

              {/* Video.js 测试按钮 */}
              {onVideoJSTest && (
                <Button
                  colorScheme="blue"
                  size="sm"
                  width="full"
                  onClick={onVideoJSTest}
                  variant="outline"
                >
                  Video.js 测试
                </Button>
              )}
            </Stack>
          </Box>

          {/* 搜索框 */}
          <Box p={4} borderBottom="1px solid" borderColor="gray.200" flexShrink={0}>
            <Box position="relative" mb={3}>
              <Input
                placeholder="搜索视频文件..."
                size="sm"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                paddingLeft="2.5rem"
              />
              <Box
                position="absolute"
                left="0.75rem"
                top="50%"
                transform="translateY(-50%)"
                color="gray.400"
              >
                <FiSearch />
              </Box>
            </Box>

            {/* 分组选项 */}
            <Stack direction="row" gap={1} flexWrap="wrap" mb={3}>
              <Button
                size="xs"
                variant={groupType === 'none' ? 'solid' : 'outline'}
                colorScheme="teal"
                onClick={() => setGroupType('none')}
              >
                不分组
              </Button>
              <Button
                size="xs"
                variant={groupType === 'date' ? 'solid' : 'outline'}
                colorScheme="teal"
                onClick={() => setGroupType('date')}
              >
                <FiCalendar />
                按日期
              </Button>
              <Button
                size="xs"
                variant={groupType === 'time' ? 'solid' : 'outline'}
                colorScheme="teal"
                onClick={() => setGroupType('time')}
              >
                <FiClock />
                按时间
              </Button>
            </Stack>

            {/* 摄像头类型选择 - 只在有多个类型时显示 */}
            {availableCameraTypes.length > 1 && (
              <Stack direction="row" gap={1} flexWrap="wrap">
                <Button
                  size="xs"
                  variant={selectedCameraType === 'all' ? 'solid' : 'outline'}
                  colorScheme="blue"
                  onClick={() => setSelectedCameraType('all')}
                >
                  全部
                </Button>
                {availableCameraTypes.map((cameraType) => {
                  const cameraInfo = getCameraPosition(`test${cameraType}.mp4`);
                  return (
                    <Button
                      key={cameraType}
                      size="xs"
                      variant={selectedCameraType === cameraType ? 'solid' : 'outline'}
                      colorScheme="blue"
                      onClick={() => setSelectedCameraType(cameraType as CameraType)}
                    >
                      <Box
                        w="8px"
                        h="8px"
                        borderRadius="full"
                        bg={cameraInfo.bgColor}
                        mr={1}
                      />
                      {cameraInfo.label}
                    </Button>
                  );
                })}
              </Stack>
            )}
          </Box>

          {/* 视频文件列表 - 独立滚动 */}
          <Box flex={1} overflowY="auto" p={4}>
            <Stack gap={2}>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                <Text fontSize="sm" color="gray.600">
                  视频文件列表
                </Text>
                {totalFiles > 0 && (
                  <Badge colorScheme="teal" size="sm">
                    {totalFiles}
                  </Badge>
                )}
              </Box>

              {isLoading ? (
                <Box textAlign="center" py={8}>
                  <Spinner size="md" color="teal.500" />
                  <Text fontSize="sm" color="gray.500" mt={2}>
                    正在扫描视频文件...
                  </Text>
                </Box>
              ) : groupedFiles.length > 0 ? (
            groupedFiles.map((group) => (
              <Box key={group.key} mb={4}>
                {/* 分组标题 - 只在有分组时显示 */}
                {groupType !== 'none' && (
                  <Box
                    display="flex"
                    alignItems="center"
                    p={2}
                    bg="gray.50"
                    borderRadius="md"
                    cursor="pointer"
                    onClick={() => toggleGroup(group.key)}
                    _hover={{ bg: "gray.100" }}
                  >
                    <Box mr={2}>
                      {group.isExpanded ? <FiChevronDown /> : <FiChevronRight />}
                    </Box>
                    <Text fontSize="sm" fontWeight="medium" flex={1}>
                      {group.label}
                    </Text>
                    <Badge colorScheme="gray" size="sm">
                      {group.files.length}
                    </Badge>
                  </Box>
                )}

                {/* 文件列表或二级分组 */}
                {(groupType === 'none' || group.isExpanded) && (
                  <Box mt={groupType === 'none' ? 0 : 2} ml={groupType === 'none' ? 0 : 4}>
                    {groupType === 'time' && group.subGroups ? (
                      // 显示二级分组
                      <Stack gap={3}>
                        {group.subGroups.map((subGroup) => (
                          <Box key={subGroup.key}>
                            {/* 二级分组标题 */}
                            <Box
                              display="flex"
                              alignItems="center"
                              p={2}
                              bg="blue.50"
                              borderRadius="md"
                              cursor="pointer"
                              onClick={() => toggleGroup(subGroup.key)}
                              _hover={{ bg: "blue.100" }}
                              border="1px solid"
                              borderColor="blue.200"
                            >
                              <Box mr={2}>
                                {subGroup.isExpanded ? <FiChevronDown /> : <FiChevronRight />}
                              </Box>
                              <Text fontSize="sm" fontWeight="medium" flex={1}>
                                {subGroup.label}
                              </Text>
                              <Badge colorScheme="blue" size="sm" mr={2}>
                                {subGroup.files.length}
                              </Badge>
                              {/* 播放按钮 */}
                              <Button
                                size="xs"
                                colorScheme="blue"
                                variant="solid"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleGroupPlay(subGroup.files);
                                }}
                              >
                                <FiPlay style={{ marginRight: '4px' }} />
                                播放
                              </Button>
                            </Box>

                            {/* 二级分组文件列表 */}
                            {subGroup.isExpanded && (
                              <Stack gap={2} mt={2} ml={4}>
                                {subGroup.files.map((file) => {
                                  const cameraPos = getCameraPosition(file.name);
                                  return (
                                    <Box
                                      key={file.id}
                                      p={3}
                                      bg="white"
                                      borderRadius="md"
                                      border="1px solid"
                                      borderColor="gray.200"
                                      cursor="pointer"
                                      _hover={{ bg: "gray.50" }}
                                      onClick={() => handleFileClick(file)}
                                    >
                                      <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                                        <Text fontSize="sm" fontWeight="medium" lineClamp={1} flex={1}>
                                          {file.name}
                                        </Text>
                                        <Box display="flex" alignItems="center" gap={2}>
                                          <MediaContentButton
                                            videoPath={file.path}
                                            screenshotCount={getScreenshotsForFile(file.path).length}
                                            clipCount={getClipsForFile(file.path).length}
                                            onClick={() => handleOpenMediaViewer(file)}
                                          />
                                          {cameraPos.label && (
                                            <Box
                                              as="span"
                                              bg={cameraPos.bgColor}
                                              color={cameraPos.textColor}
                                              px={2}
                                              py={1}
                                              borderRadius="md"
                                              fontSize="xs"
                                              fontWeight="bold"
                                              minW="24px"
                                              textAlign="center"
                                              boxShadow="sm"
                                            >
                                              {cameraPos.label}
                                            </Box>
                                          )}
                                        </Box>
                                      </Box>
                                      <Text fontSize="xs" color="gray.500">
                                        {formatTimestamp(file.timestamp)}
                                      </Text>
                                      <Text fontSize="xs" color="gray.500">
                                        {formatFileSize(file.size)}
                                      </Text>

                                    </Box>
                                  );
                                })}
                              </Stack>
                            )}
                          </Box>
                        ))}
                      </Stack>
                    ) : (
                      // 显示普通文件列表
                      <Stack gap={2}>
                        {group.files.map((file) => {
                          const cameraPos = getCameraPosition(file.name);
                          return (
                            <Box
                              key={file.id}
                              p={3}
                              bg="white"
                              borderRadius="md"
                              border="1px solid"
                              borderColor="gray.200"
                              cursor="pointer"
                              _hover={{ bg: "gray.50" }}
                              onClick={() => handleFileClick(file)}
                            >
                              <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                                <Text fontSize="sm" fontWeight="medium" lineClamp={1} flex={1}>
                                  {file.name}
                                </Text>
                                <Box display="flex" alignItems="center" gap={2}>
                                  <MediaContentButton
                                    videoPath={file.path}
                                    screenshotCount={getScreenshotsForFile(file.path).length}
                                    clipCount={getClipsForFile(file.path).length}
                                    onClick={() => handleOpenMediaViewer(file)}
                                  />
                                  {cameraPos.label && (
                                    <Box
                                      as="span"
                                      bg={cameraPos.bgColor}
                                      color={cameraPos.textColor}
                                      px={2}
                                      py={1}
                                      borderRadius="md"
                                      fontSize="xs"
                                      fontWeight="bold"
                                      minW="24px"
                                      textAlign="center"
                                      boxShadow="sm"
                                    >
                                      {cameraPos.label}
                                    </Box>
                                  )}
                                </Box>
                              </Box>
                              <Text fontSize="xs" color="gray.500">
                                {formatTimestamp(file.timestamp)}
                              </Text>
                              <Text fontSize="xs" color="gray.500">
                                {formatFileSize(file.size)}
                              </Text>

                            </Box>
                          );
                        })}
                      </Stack>
                    )}
                  </Box>
                )}
              </Box>
                ))
              ) : searchQuery ? (
                <Box textAlign="center" py={8}>
                  <FiVideo size={24} color="gray" />
                  <Text fontSize="sm" color="gray.500" mt={2}>
                    未找到匹配的视频文件
                  </Text>
                </Box>
              ) : selectedFolder ? (
                <Box textAlign="center" py={8}>
                  <FiVideo size={24} color="gray" />
                  <Text fontSize="sm" color="gray.500" mt={2}>
                    该文件夹中没有视频文件
                  </Text>
                </Box>
              ) : (
                <Box textAlign="center" py={8}>
                  <FiFolder size={24} color="gray" />
                  <Text fontSize="sm" color="gray.500" mt={2}>
                    请先选择视频文件夹
                  </Text>
                </Box>
              )}
            </Stack>
          </Box>
        </>
      )}

      {/* 媒体内容查看器 */}
      {selectedVideoForMedia && (
        <MediaContentViewer
          open={mediaViewerOpen}
          onOpenChange={(details) => {
            if (!details.open) {
              handleCloseMediaViewer();
            }
          }}
          videoPath={selectedVideoForMedia.path}
          videoName={selectedVideoForMedia.name}
          screenshots={getScreenshotsForFile(selectedVideoForMedia.path)}
          clips={getClipsForFile(selectedVideoForMedia.path)}
          groups={getGroupsForFile(selectedVideoForMedia.path)}
          onRemoveScreenshot={(screenshotId) => removeScreenshot(selectedVideoForMedia.path, screenshotId)}
          onRemoveClip={(clipId) => removeClip(selectedVideoForMedia.path, clipId)}
          onRemoveGroup={removeGroup}
          onOpenScreenshot={handleOpenScreenshot}
          onPlayClip={handlePlayClip}
          onOpenInFolder={handleOpenInFolder}
        />
      )}
    </Box>
  );
};

export default VideoFileManager;
