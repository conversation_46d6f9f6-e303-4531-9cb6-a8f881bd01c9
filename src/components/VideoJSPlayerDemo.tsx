import React, { useRef, useState } from 'react';
import { <PERSON>, Button, Stack, Text, Slider } from '@chakra-ui/react';
import VideoJSPlayer, { VideoJSPlayerRef } from './VideoJSPlayer';
import { useVideoJSPlayer } from '../hooks/useVideoJSPlayer';
import VideoPlayerControls from './VideoPlayerControls';

interface VideoJSPlayerDemoProps {
  videoPath?: string;
  onClose?: () => void;
}

const VideoJSPlayerDemo: React.FC<VideoJSPlayerDemoProps> = ({
  videoPath,
  onClose
}) => {
  const playerRef = useRef<VideoJSPlayerRef>(null);
  const [videoSrc, setVideoSrc] = useState(videoPath);

  // 使用Video.js播放器状态管理
  const {
    state,
    actions,
    getVideoJSEventHandlers
  } = useVideoJSPlayer({
    onTimeUpdate: (currentTime, duration) => {
      console.log(`Time update: ${currentTime}/${duration}`);
    },
    onPlay: () => {
      console.log('Video started playing');
    },
    onPause: () => {
      console.log('Video paused');
    },
    onError: (error) => {
      console.error('Video error:', error);
    }
  });

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleSeek = (time: number) => {
    actions.seek(time);
  };

  const handleVolumeChange = (volume: number) => {
    actions.setVolume(volume);
  };

  return (
    <Box
      width="100%"
      height="100%"
      bg="black"
      position="relative"
      display="flex"
      flexDirection="column"
    >
      {/* 视频播放器 */}
      <Box flex={1} position="relative">
        {videoSrc ? (
          <VideoJSPlayer
            ref={playerRef}
            src={`file://${videoSrc}`}
            width="100%"
            height="100%"
            fluid={true}
            responsive={true}
            controls={false}
            preload="metadata"
            {...getVideoJSEventHandlers()}
          />
        ) : (
          <Box
            width="100%"
            height="100%"
            display="flex"
            alignItems="center"
            justifyContent="center"
            color="white"
            fontSize="lg"
          >
            请选择一个视频文件
          </Box>
        )}
      </Box>

      {/* 自定义控制栏 */}
      {videoSrc && (
        <Box
          position="absolute"
          bottom={0}
          left={0}
          right={0}
          bg="rgba(0,0,0,0.8)"
          p={4}
        >
          <VideoPlayerControls
            isPlaying={state.isPlaying}
            currentTime={state.currentTime}
            duration={state.duration}
            volume={state.volume}
            isMuted={state.isMuted}
            isFullscreen={state.isFullscreen}
            isLoading={state.isLoading}
            onTogglePlay={actions.togglePlay}
            onSeek={handleSeek}
            onVolumeChange={handleVolumeChange}
            onToggleMute={actions.toggleMute}
            onToggleFullscreen={actions.toggleFullscreen}
            onSkipTime={actions.skipTime}
            showVolumeControl={true}
            showSkipButtons={true}
            showFullscreenButton={true}
            compact={false}
            videoPath={videoSrc}
            onScreenshotTaken={(path) => {
              console.log('Screenshot saved:', path);
            }}
          />
        </Box>
      )}

      {/* 调试信息 */}
      <Box
        position="absolute"
        top={4}
        right={4}
        bg="rgba(0,0,0,0.8)"
        color="white"
        p={3}
        borderRadius="md"
        fontSize="sm"
        maxWidth="300px"
      >
        <Text fontWeight="bold" mb={2}>Video.js 播放器状态</Text>
        <Stack spacing={1}>
          <Text>播放状态: {state.isPlaying ? '播放中' : '暂停'}</Text>
          <Text>当前时间: {formatTime(state.currentTime)}</Text>
          <Text>总时长: {formatTime(state.duration)}</Text>
          <Text>音量: {Math.round(state.volume * 100)}%</Text>
          <Text>静音: {state.isMuted ? '是' : '否'}</Text>
          <Text>全屏: {state.isFullscreen ? '是' : '否'}</Text>
          <Text>加载中: {state.isLoading ? '是' : '否'}</Text>
          <Text>播放速度: {state.playbackRate}x</Text>
          <Text>就绪状态: {state.isReady ? '就绪' : '未就绪'}</Text>
          {state.hasError && (
            <Text color="red.300">错误: {state.errorMessage}</Text>
          )}
        </Stack>
      </Box>

      {/* 关闭按钮 */}
      {onClose && (
        <Button
          position="absolute"
          top={4}
          left={4}
          size="sm"
          onClick={onClose}
          colorScheme="gray"
        >
          关闭
        </Button>
      )}
    </Box>
  );
};

export default VideoJSPlayerDemo;
