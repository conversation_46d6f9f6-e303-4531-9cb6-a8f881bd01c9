import React, { useRef, useEffect, useState } from 'react';
import { Box, Button, Text, VStack, HStack, Input } from '@chakra-ui/react';
import videojs from 'video.js';
import 'video.js/dist/video-js.css';

const SimpleVideoJSTest: React.FC = () => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const playerRef = useRef<videojs.Player | null>(null);
  const [videoPath, setVideoPath] = useState('');
  const [isReady, setIsReady] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!videoRef.current) return;

    try {
      // 创建最简单的Video.js播放器
      const player = videojs(videoRef.current, {
        controls: true,
        fluid: true,
        responsive: true,
        preload: 'metadata',
        html5: {
          nativeTextTracks: true,
          nativeAudioTracks: true,
          nativeVideoTracks: true,
        },
        techOrder: ['html5'],
        suppressNotSupportedError: true,
      });

      playerRef.current = player;

      player.ready(() => {
        console.log('Simple VideoJS Player is ready');
        setIsReady(true);
        setError(null);
      });

      player.on('error', (e) => {
        console.error('VideoJS Error:', e);
        setError('播放器初始化失败');
      });

      return () => {
        if (playerRef.current) {
          playerRef.current.dispose();
          playerRef.current = null;
        }
      };
    } catch (err) {
      console.error('Failed to create VideoJS player:', err);
      setError(`创建播放器失败: ${err}`);
    }
  }, []);

  const loadVideo = () => {
    if (!playerRef.current || !videoPath.trim()) return;

    try {
      playerRef.current.src({
        src: `file://${videoPath}`,
        type: 'video/mp4'
      });
      console.log('Video loaded:', videoPath);
    } catch (err) {
      console.error('Failed to load video:', err);
      setError(`加载视频失败: ${err}`);
    }
  };

  const testPlay = () => {
    if (!playerRef.current) return;
    
    playerRef.current.play().catch((err) => {
      console.error('Play failed:', err);
      setError(`播放失败: ${err}`);
    });
  };

  const testPause = () => {
    if (!playerRef.current) return;
    playerRef.current.pause();
  };

  return (
    <Box p={6} maxWidth="800px" mx="auto">
      <VStack spacing={4} align="stretch">
        <Text fontSize="xl" fontWeight="bold">
          简单 Video.js 测试
        </Text>

        {error && (
          <Box bg="red.100" color="red.800" p={3} borderRadius="md">
            错误: {error}
          </Box>
        )}

        <Box bg={isReady ? 'green.100' : 'yellow.100'} p={3} borderRadius="md">
          <Text color={isReady ? 'green.800' : 'yellow.800'}>
            播放器状态: {isReady ? '就绪' : '初始化中...'}
          </Text>
        </Box>

        <HStack>
          <Input
            placeholder="输入视频文件路径"
            value={videoPath}
            onChange={(e) => setVideoPath(e.target.value)}
          />
          <Button onClick={loadVideo} colorScheme="blue" disabled={!isReady}>
            加载视频
          </Button>
        </HStack>

        <HStack spacing={4}>
          <Button onClick={testPlay} colorScheme="green" disabled={!isReady}>
            播放
          </Button>
          <Button onClick={testPause} colorScheme="red" disabled={!isReady}>
            暂停
          </Button>
        </HStack>

        <Box
          height="400px"
          bg="black"
          borderRadius="lg"
          overflow="hidden"
        >
          <video
            ref={videoRef}
            className="video-js vjs-default-skin"
            data-setup="{}"
            style={{
              width: '100%',
              height: '100%',
            }}
          />
        </Box>

        <Box bg="gray.100" p={3} borderRadius="md" fontSize="sm">
          <Text fontWeight="bold" mb={2}>调试信息:</Text>
          <Text>Video.js 版本: {videojs.VERSION}</Text>
          <Text>播放器就绪: {isReady ? '是' : '否'}</Text>
          <Text>当前视频: {videoPath || '未设置'}</Text>
        </Box>
      </VStack>
    </Box>
  );
};

export default SimpleVideoJSTest;
