import { Box, Text, Spinner, Button } from '@chakra-ui/react';
import { useState, useCallback, useEffect, useRef } from 'react';
import VideoFileManager from './components/VideoFileManager';
import MultiVideoPlayer, { MultiVideoPlayerRef } from './components/MultiVideoPlayer';
// import MultiVideoClipControls from './components/MultiVideoClipControls'; // 已移除，功能集成到主控制栏
import VideoPlayerControls from './components/VideoPlayerControls';
import MapComponent from './components/MapComponent';
import GPSInfoPanel from './components/GPSInfoPanel';
import ApiKeyConfigModal from './components/ApiKeyConfigModal';
import AuthCodeInput from './components/AuthCodeInput';
import SimpleVideoJSTest from './components/SimpleVideoJSTest';
import { VideoFile, GPSTrack, GPSPoint } from './types/electron';
import { useFileMediaContent } from './hooks/useFileMediaContent';
import { createLogger } from './utils/logger';
import { toaster } from './ui/toaster';

// 创建日志实例
const logger = createLogger('App');

function App() {
  // 认证状态
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isCheckingLicense, setIsCheckingLicense] = useState(true);

  // Video.js 测试模式
  const [isVideoJSTestMode, setIsVideoJSTestMode] = useState(false);

  const [_allVideoFiles, setAllVideoFiles] = useState<VideoFile[]>([]);
  const [_currentVideoFile, setCurrentVideoFile] = useState<VideoFile | undefined>();
  const [_currentTime, setCurrentTime] = useState(0);
  const [_videoDuration, setVideoDuration] = useState(0);
  const [currentGPSTrack, setCurrentGPSTrack] = useState<GPSTrack | undefined>();
  const [currentGPSPoint, setCurrentGPSPoint] = useState<GPSPoint | undefined>();
  const [isExtractingGPS, setIsExtractingGPS] = useState(false);

  // 保留用于兼容性的状态（将逐步移除）
  const [singleVideoIsPlaying, setSingleVideoIsPlaying] = useState<boolean>(false);
  const [singleVideoCurrentTime, setSingleVideoCurrentTime] = useState<number>(0);
  const [amapApiKey, setAmapApiKey] = useState<string | null>(null);
  const [isConfigModalOpen, setIsConfigModalOpen] = useState(false);
  const [isFileListCollapsed, setIsFileListCollapsed] = useState(false);

  // 统一视频播放状态 - 支持单视频和多视频
  const [videoFiles, setVideoFiles] = useState<VideoFile[]>([]);
  const [isPlaying, setIsPlaying] = useState(false);
  const [masterTime, setMasterTime] = useState(0);
  const [masterDuration, setMasterDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);

  // 移除单视频模式标识，统一按多视频处理
  // const isSingleVideoMode = videoFiles.length === 1;

  // 主视频独占模式状态
  const [isMainVideoFocused, setIsMainVideoFocused] = useState(false);

  // 显示的视频数量
  const [visibleVideoCount, setVisibleVideoCount] = useState(0);

  // 视频可见性状态
  const [videoVisibility, setVideoVisibility] = useState<boolean[]>([]);

  // 包装setVideoVisibility以添加调试信息，并保存最新状态的引用
  const latestVideoVisibilityRef = useRef<boolean[]>([]);

  // 初始化ref
  useEffect(() => {
    latestVideoVisibilityRef.current = videoVisibility;
  }, [videoVisibility]);

  const handleVideoVisibilityChange = useCallback((newVisibility: boolean[]) => {
    // 立即更新ref，确保其他函数能获取到最新状态
    latestVideoVisibilityRef.current = newVisibility;

    setVideoVisibility(newVisibility);
  }, []);

  // 缩放控制状态
  const [scale, setScale] = useState(1);
  const [showZoomControls, setShowZoomControls] = useState(false);

  // 当视频文件变化时，初始化视频可见性状态
  useEffect(() => {
    if (videoFiles.length > 0) {
      setVideoVisibility(new Array(videoFiles.length).fill(true));
    } else {
      setVideoVisibility([]);
    }
  }, [videoFiles.length]);

  // MultiVideoPlayer的ref
  const multiVideoPlayerRef = useRef<MultiVideoPlayerRef>(null);

  // 布局占比状态 - 视频区域占比（GPS区域占比 = 1 - videoAreaRatio）
  const [videoAreaRatio, setVideoAreaRatio] = useState<number>(() => {
    // 从本地存储读取用户设置，默认值为0.7（视频区域占70%）
    const saved = localStorage.getItem('meea-video-gps-ratio');
    return saved ? parseFloat(saved) : 0.7;
  });

  // 剪辑模式状态
  const [isClipMode, setIsClipMode] = useState(false);
  const [clipStartTime, setClipStartTime] = useState(0);
  const [clipEndTime, setClipEndTime] = useState(0);
  const [isClipping, setIsClipping] = useState(false);
  const [clipProgress, setClipProgress] = useState(0);



  // 记录是否从多视频模式切换过来的状态
  const [previousMultiVideoFiles, setPreviousMultiVideoFiles] = useState<VideoFile[]>([]);

  // 加载配置和验证许可证
  useEffect(() => {
    const initializeApp = async () => {
      try {
        // 1. 加载API Key配置
        const apiKey = await window.electronAPI.getAmapApiKey();
        setAmapApiKey(apiKey);

        // 2. 验证许可证
        const licenseResult = await window.electronAPI.license.validate();

        if (licenseResult.success && licenseResult.isValid) {
          // 许可证有效，直接进入应用
          setIsAuthenticated(true);
        } else {
          // 许可证无效，需要重新验证
          setIsAuthenticated(false);
        }
      } catch (error) {
        console.error('应用初始化失败:', error);
        // 发生错误时，要求重新验证
        setIsAuthenticated(false);
      } finally {
        setIsCheckingLicense(false);
      }
    };

    initializeApp();
  }, []);

  // 处理App Key更新
  const handleApiKeyUpdate = useCallback(async (newApiKey: string | null) => {
    try {
      await window.electronAPI.setAmapApiKey(newApiKey);
      setAmapApiKey(newApiKey);
    } catch (error) {
      console.error('保存App Key失败:', error);
    }
  }, []);

  // 处理打开配置弹窗
  const handleOpenConfigModal = useCallback(() => {
    setIsConfigModalOpen(true);
  }, []);

  // 媒体内容管理
  const mediaContentManager = useFileMediaContent();

  // 处理认证成功
  const handleAuthSuccess = useCallback(() => {
    setIsAuthenticated(true);
  }, []);

  // 处理文件选择 - 统一处理单视频和多视频
  const handleFileSelect = useCallback(async (file: VideoFile, options?: {
    seekTime?: number;
    shouldPlay?: boolean;
  }) => {
    const { seekTime, shouldPlay } = options || {};

    // 设置为单视频模式
    setVideoFiles([file]);

    // 清除之前的多视频文件记录
    setPreviousMultiVideoFiles([]);

    // 重置播放进度到开始位置，除非明确指定了seekTime
    if (seekTime === undefined) {
      setMasterTime(0);
      setIsPlaying(false); // 重置为默认播放状态（暂停）
    }

    // 重置缩放状态
    setScale(1);
    setShowZoomControls(false);

    // 重置主视频独占模式状态
    setIsMainVideoFocused(false);

    // 通知MultiVideoPlayer重置缩放和主视频独占状态
    setTimeout(() => {
      multiVideoPlayerRef.current?.resetZoom?.();
      multiVideoPlayerRef.current?.returnToMultiVideoLayout?.();
    }, 100);

    // 保存当前文件信息（用于兼容性）
    setCurrentVideoFile(file);

    // 如果需要跳转到特定时间，设置时间
    if (seekTime !== undefined) {
      setMasterTime(seekTime);
    }

    // 如果需要自动播放，设置播放状态
    if (shouldPlay !== undefined) {
      setIsPlaying(shouldPlay);
    }

    // 提取GPS数据
    if (!file.gpsData) {
      setIsExtractingGPS(true);
      try {
        const gpsTrack = await window.electronAPI.extractGPSData(file.path);
        if (gpsTrack) {
          setCurrentGPSTrack(gpsTrack);
          // 立即设置第一个GPS点作为当前GPS点
          if (gpsTrack.points && gpsTrack.points.length > 0) {
            setCurrentGPSPoint(gpsTrack.points[0]);
          }
          // 更新文件对象中的GPS数据
          file.gpsData = gpsTrack;
        } else {
          setCurrentGPSTrack(undefined);
          setCurrentGPSPoint(undefined);
        }
      } catch (error) {
        console.error('提取GPS数据失败:', error);
        setCurrentGPSTrack(undefined);
        setCurrentGPSPoint(undefined);
      } finally {
        setIsExtractingGPS(false);
      }
    } else {
      // 使用已缓存的GPS数据
      setCurrentGPSTrack(file.gpsData);
      // 立即设置第一个GPS点作为当前GPS点
      if (file.gpsData && file.gpsData.points && file.gpsData.points.length > 0) {
        setCurrentGPSPoint(file.gpsData.points[0]);
      }
    }
  }, []);

  // 处理视频时间更新 - 统一处理
  const handleTimeUpdate = useCallback((time: number, duration: number) => {
    setCurrentTime(time);
    setVideoDuration(duration);

    // 更新统一播放器状态
    setMasterTime(time);
    setMasterDuration(duration);

    // 更新单视频播放时间（保持兼容性）
    setSingleVideoCurrentTime(time);

    // 根据当前时间从GPS轨迹中获取对应的GPS点
    if (currentGPSTrack && currentGPSTrack.points.length > 0) {
      // 简化的时间映射：直接使用视频播放进度映射到GPS点索引
      const progress = duration > 0 ? time / duration : 0;
      const pointIndex = Math.floor(progress * (currentGPSTrack.points.length - 1));
      const clampedIndex = Math.min(Math.max(pointIndex, 0), currentGPSTrack.points.length - 1);
      const gpsPoint = currentGPSTrack.points[clampedIndex];

      setCurrentGPSPoint(gpsPoint);
    } else {
      setCurrentGPSPoint(undefined);
    }
  }, [currentGPSTrack]);



  // 处理单视频播放器播放状态变化（保留用于兼容性）
  const handleSingleVideoPlayStateChange = useCallback(() => {
    setSingleVideoIsPlaying(true);
  }, []);

  const handleSingleVideoPause = useCallback(() => {
    setSingleVideoIsPlaying(false);
  }, []);

  // 处理文件列表更新
  const handleFilesUpdate = useCallback((files: VideoFile[]) => {
    setAllVideoFiles(files);
  }, []);

  // 处理多视频播放 - 统一处理
  const handleVideoPlay = useCallback(async (files: VideoFile[]) => {
    setVideoFiles(files);

    // 初始化视频可见性状态 - 默认所有视频都可见
    const initialVisibility = new Array(files.length).fill(true);

    setVideoVisibility(initialVisibility);

    // 清除单视频状态（保留用于兼容性）
    setCurrentVideoFile(undefined);
    // 清除之前的多视频文件记录
    setPreviousMultiVideoFiles([]);

    // 重置播放进度到开始位置
    setMasterTime(0);
    setIsPlaying(false); // 重置为默认播放状态（暂停）

    // 重置缩放状态
    setScale(1);
    setShowZoomControls(false);

    // 重置主视频独占模式状态
    setIsMainVideoFocused(false);

    // 通知MultiVideoPlayer重置缩放和主视频独占状态
    setTimeout(() => {
      multiVideoPlayerRef.current?.resetZoom?.();
      multiVideoPlayerRef.current?.returnToMultiVideoLayout?.();
    }, 100);

    // 选择第一个视频文件来解析GPS数据
    if (files.length > 0) {
      const selectedFile = files[0]; // 选择第一个视频

      // 提取GPS数据
      if (!selectedFile.gpsData) {
        setIsExtractingGPS(true);
        try {
          const gpsTrack = await window.electronAPI.extractGPSData(selectedFile.path);
          if (gpsTrack) {
            setCurrentGPSTrack(gpsTrack);
            // 立即设置第一个GPS点作为当前GPS点
            if (gpsTrack.points && gpsTrack.points.length > 0) {
              setCurrentGPSPoint(gpsTrack.points[0]);
            }
            // 更新文件对象中的GPS数据
            selectedFile.gpsData = gpsTrack;
          } else {
            setCurrentGPSTrack(undefined);
            setCurrentGPSPoint(undefined);
          }
        } catch (error) {
          console.error('多视频模式：提取GPS数据失败:', error);
          setCurrentGPSTrack(undefined);
        } finally {
          setIsExtractingGPS(false);
        }
      } else {
        // 使用已缓存的GPS数据
        setCurrentGPSTrack(selectedFile.gpsData);
        // 立即设置第一个GPS点作为当前GPS点
        if (selectedFile.gpsData && selectedFile.gpsData.points && selectedFile.gpsData.points.length > 0) {
          setCurrentGPSPoint(selectedFile.gpsData.points[0]);
        }
      }
    } else {
      setCurrentGPSTrack(undefined);
      setCurrentGPSPoint(undefined);
    }
  }, []);

  // 关闭视频播放
  const handleCloseVideo = useCallback(() => {
    setVideoFiles([]);
    // 清除之前的多视频文件记录
    setPreviousMultiVideoFiles([]);
  }, []);

  // 处理单独播放（从多视频播放器中点击单个视频）- 现在改为在当前布局中展示
  const handleSingleVideoPlay = useCallback((file: VideoFile) => {
    // 保存当前的多视频文件列表，以便返回
    setPreviousMultiVideoFiles(videoFiles);
    // 切换到单视频模式，传递当前播放状态
    handleFileSelect(file, {
      seekTime: masterTime,
      shouldPlay: isPlaying
    });
  }, [handleFileSelect, videoFiles, masterTime, isPlaying]);

  // 返回多视频播放模式
  const handleReturnToMultiVideo = useCallback(() => {
    if (previousMultiVideoFiles.length > 0) {
      // 保存当前播放状态，用于恢复多视频播放状态
      const shouldContinuePlay = isPlaying;
      const currentTime = masterTime;

      // 恢复多视频模式
      setVideoFiles(previousMultiVideoFiles);

      // 重置缩放状态
      setScale(1);
      setShowZoomControls(false);

      // 延迟设置播放状态，确保视频加载完成后再应用
      setTimeout(() => {
        // 重置缩放
        multiVideoPlayerRef.current?.resetZoom?.();
        setMasterTime(currentTime);
        // 再次延迟设置播放状态，确保时间跳转完成后再开始播放
        setTimeout(() => {
          setIsPlaying(shouldContinuePlay);
        }, 200);
      }, 100);

      // 清除单视频状态（保留用于兼容性）
      setCurrentVideoFile(undefined);
      setSingleVideoIsPlaying(false);
      setSingleVideoCurrentTime(0);

      // 注意：不清除GPS数据，保持地图状态
      // setCurrentGPSTrack(undefined);
      // setCurrentGPSPoint(undefined);
      // 清除之前的多视频文件记录
      setPreviousMultiVideoFiles([]);
    }
  }, [previousMultiVideoFiles, isPlaying, masterTime]);

  // 处理主视频独占模式变化
  const handleMainVideoFocusChange = useCallback((isFocused: boolean) => {
    setIsMainVideoFocused(isFocused);
  }, []);

  // 处理显示视频数量变化
  const handleVisibleVideoCountChange = useCallback((count: number) => {
    try {
      setVisibleVideoCount(count);
    } catch (error) {
      console.error('Error setting visible video count:', error);
    }
  }, []);

  // 处理缩放状态变化
  const handleZoomStateChange = useCallback((newScale: number, newShowZoomControls: boolean) => {
    setScale(newScale);
    setShowZoomControls(newShowZoomControls);
  }, []);

  // 返回多视频布局
  const handleReturnToMultiVideoLayout = useCallback(() => {
    multiVideoPlayerRef.current?.returnToMultiVideoLayout();
  }, []);

  // 保存布局占比到本地存储
  const saveVideoAreaRatio = useCallback((ratio: number) => {
    localStorage.setItem('meea-video-gps-ratio', ratio.toString());
    setVideoAreaRatio(ratio);
  }, []);

  // 拖拽状态
  const [isDragging, setIsDragging] = useState(false);

  // 处理拖拽调整占比
  const handleResizeStart = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    const startX = e.clientX;
    const containerWidth = window.innerWidth - 24; // 减去padding
    const startRatio = videoAreaRatio;
    let currentRatio = startRatio;

    setIsDragging(true);

    const handleMouseMove = (moveEvent: MouseEvent) => {
      const deltaX = moveEvent.clientX - startX;
      const deltaRatio = deltaX / containerWidth;
      const newRatio = Math.max(0.3, Math.min(0.8, startRatio + deltaRatio)); // 限制在30%-80%之间
      currentRatio = newRatio;
      setVideoAreaRatio(newRatio);
    };

    const handleMouseUp = () => {
      // 保存最终的占比到本地存储
      saveVideoAreaRatio(currentRatio);
      setIsDragging(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    document.body.style.cursor = 'col-resize';
    document.body.style.userSelect = 'none';
  }, [videoAreaRatio, saveVideoAreaRatio]);

  // 渲染地图和GPS信息组件（共享组件，避免重复初始化）
  const renderMapAndGPSInfo = useCallback(() => {
    return (
      <>
        {/* GPS加载状态指示器 */}
        {isExtractingGPS && (
          <Box
            position="absolute"
            top={3}
            right={3}
            zIndex={10}
            bg="rgba(255, 255, 255, 0.95)"
            borderRadius="md"
            px={3}
            py={2}
            display="flex"
            alignItems="center"
            gap={2}
            boxShadow="md"
            backdropFilter="blur(4px)"
          >
            <Spinner size="sm" color="blue.500" />
            <Text fontSize="sm" color="gray.700">
              提取GPS数据...
            </Text>
          </Box>
        )}

        {/* 地图组件 - 使用固定key确保实例稳定 */}
        <MapComponent
          key="shared-map" // 固定key，确保地图实例不会重复创建
          height="100%"
          center={currentGPSPoint ? [currentGPSPoint.longitude, currentGPSPoint.latitude] : undefined}
          gpsTrack={currentGPSTrack}
          currentPosition={currentGPSPoint}
          apiKey={amapApiKey}
          onConfigureApiKey={handleOpenConfigModal}
        />
      </>
    );
  }, [currentGPSPoint, currentGPSTrack, amapApiKey, isExtractingGPS, handleOpenConfigModal]);

  // 统一视频播放器控制函数
  const handleVideoPlayControl = useCallback(() => {
    setIsPlaying(true);
    // 同步更新兼容性状态
    setSingleVideoIsPlaying(true);
    // 实际调用播放器播放方法，不通知父组件避免循环调用
    multiVideoPlayerRef.current?.play(false);
  }, []);

  const handleVideoPause = useCallback(() => {
    setIsPlaying(false);
    // 同步更新兼容性状态
    setSingleVideoIsPlaying(false);
    // 实际调用播放器暂停方法，不通知父组件避免循环调用
    multiVideoPlayerRef.current?.pause(false);
  }, []);

  const handleVideoTogglePlay = useCallback(() => {
    // 只有在有视频文件时才允许播放控制
    if (videoFiles.length === 0) {
      return;
    }

    if (isPlaying) {
      handleVideoPause();
      // handleVideoPause 已经包含了播放器调用，不需要重复调用
    } else {
      handleVideoPlayControl();
      // handleVideoPlayControl 已经包含了播放器调用，不需要重复调用
    }
  }, [videoFiles.length, isPlaying, handleVideoPause, handleVideoPlayControl]);

  const handleVideoSeek = useCallback((time: number) => {
    // 立即更新UI状态，提供即时的视觉反馈
    setMasterTime(time);
    // 同时调用 MultiVideoPlayer 的 seek 方法进行实际跳转
    multiVideoPlayerRef.current?.seek(time);
  }, []);

  const handleVideoVolumeChange = useCallback((volume: number) => {
    setVolume(volume);
    setIsMuted(volume === 0);
  }, []);

  const handleVideoToggleMute = useCallback(() => {
    setIsMuted(prev => !prev);
  }, []);

  const handleVideoSkipTime = useCallback((seconds: number) => {
    const newTime = Math.max(0, Math.min(masterDuration, masterTime + seconds));
    setMasterTime(newTime);
    // 直接调用 MultiVideoPlayer 的 seek 方法，不通过回调
    multiVideoPlayerRef.current?.seek(newTime);
  }, [masterTime, masterDuration]);

  // 帧控制相关状态
  const longPressTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const continuousIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const isLongPressRef = useRef(false);
  const pressedKeysRef = useRef<Set<string>>(new Set());

  // 帧控制函数
  const frameTime = 1 / 30; // 30fps

  const stepForward = useCallback(() => {
    if (videoFiles.length === 0) return;
    const newTime = Math.min(masterDuration, masterTime + frameTime);
    setMasterTime(newTime);
    multiVideoPlayerRef.current?.seek(newTime);
  }, [videoFiles.length, masterTime, masterDuration, frameTime]);

  const stepBackward = useCallback(() => {
    if (videoFiles.length === 0) return;
    const newTime = Math.max(0, masterTime - frameTime);
    setMasterTime(newTime);
    multiVideoPlayerRef.current?.seek(newTime);
  }, [videoFiles.length, masterTime, frameTime]);

  const startContinuousForward = useCallback(() => {
    if (videoFiles.length === 0) return;

    // 清除之前的定时器
    if (continuousIntervalRef.current) {
      clearInterval(continuousIntervalRef.current);
    }

    // 设置连续移动定时器
    continuousIntervalRef.current = setInterval(() => {
      setMasterTime(prevTime => {
        const newTime = Math.min(masterDuration, prevTime + frameTime);
        multiVideoPlayerRef.current?.seek(newTime);

        if (newTime >= masterDuration) {
          // 到达结尾，停止连续移动
          if (continuousIntervalRef.current) {
            clearInterval(continuousIntervalRef.current);
            continuousIntervalRef.current = null;
          }
        }

        return newTime;
      });
    }, frameTime * 1000);
  }, [videoFiles.length, masterDuration, frameTime]);

  const startContinuousBackward = useCallback(() => {
    if (videoFiles.length === 0) return;

    // 清除之前的定时器
    if (continuousIntervalRef.current) {
      clearInterval(continuousIntervalRef.current);
    }

    // 设置连续移动定时器
    continuousIntervalRef.current = setInterval(() => {
      setMasterTime(prevTime => {
        const newTime = Math.max(0, prevTime - frameTime);
        multiVideoPlayerRef.current?.seek(newTime);

        if (newTime <= 0) {
          // 到达开头，停止连续移动
          if (continuousIntervalRef.current) {
            clearInterval(continuousIntervalRef.current);
            continuousIntervalRef.current = null;
          }
        }

        return newTime;
      });
    }, frameTime * 1000);
  }, [videoFiles.length, frameTime]);

  const stopContinuous = useCallback(() => {
    if (continuousIntervalRef.current) {
      clearInterval(continuousIntervalRef.current);
      continuousIntervalRef.current = null;
    }
    if (longPressTimeoutRef.current) {
      clearTimeout(longPressTimeoutRef.current);
      longPressTimeoutRef.current = null;
    }
    isLongPressRef.current = false;
  }, []);

  // 全局键盘事件监听 - 控制视频播放/暂停和帧控制
  useEffect(() => {
    const handleGlobalKeyDown = (event: KeyboardEvent) => {
      // 检查是否在输入框中，如果是则不处理快捷键
      const target = event.target as HTMLElement;
      if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable) {
        // 对于输入框，只阻止视频控制快捷键，不阻止粘贴等其他快捷键
        if (event.code === 'Space' || event.code === 'ArrowLeft' || event.code === 'ArrowRight') {
          return; // 不处理视频控制快捷键
        }
        // 其他快捷键（如Ctrl+V）让它们正常传播
        return;
      }

      // 只有在有视频文件时才处理视频相关的键盘事件
      const hasVideo = videoFiles.length > 0;

      switch (event.code) {
        case 'Space':
          if (hasVideo) {
            event.preventDefault();
            event.stopPropagation();
            handleVideoTogglePlay();
          }
          break;

        case 'ArrowLeft':
          if (hasVideo) {
            event.preventDefault();
            event.stopPropagation();

            // 避免重复处理同一个按键
            if (pressedKeysRef.current.has(event.code)) {
              return;
            }
            pressedKeysRef.current.add(event.code);

            // 立即执行一次单步后退
            stepBackward();

            // 设置长按检测
            isLongPressRef.current = false;
            longPressTimeoutRef.current = setTimeout(() => {
              isLongPressRef.current = true;
              startContinuousBackward();
            }, 300);
          }
          break;

        case 'ArrowRight':
          if (hasVideo) {
            event.preventDefault();
            event.stopPropagation();

            // 避免重复处理同一个按键
            if (pressedKeysRef.current.has(event.code)) {
              return;
            }
            pressedKeysRef.current.add(event.code);

            // 立即执行一次单步前进
            stepForward();

            // 设置长按检测
            isLongPressRef.current = false;
            longPressTimeoutRef.current = setTimeout(() => {
              isLongPressRef.current = true;
              startContinuousForward();
            }, 300);
          }
          break;
      }
    };

    const handleGlobalKeyUp = (event: KeyboardEvent) => {
      // 检查是否在输入框中，如果是则不处理快捷键
      const target = event.target as HTMLElement;
      if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable) {
        // 对于输入框，只阻止视频控制快捷键
        if (event.code === 'Space' || event.code === 'ArrowLeft' || event.code === 'ArrowRight') {
          return; // 不处理视频控制快捷键
        }
        return;
      }

      const hasVideo = videoFiles.length > 0;

      switch (event.code) {
        case 'ArrowLeft':
        case 'ArrowRight':
          if (hasVideo) {
            event.preventDefault();
            event.stopPropagation();

            pressedKeysRef.current.delete(event.code);

            // 清除长按检测定时器
            if (longPressTimeoutRef.current) {
              clearTimeout(longPressTimeoutRef.current);
              longPressTimeoutRef.current = null;
            }

            // 如果是长按状态，停止连续移动
            if (isLongPressRef.current) {
              stopContinuous();
              isLongPressRef.current = false;
            }
          }
          break;
      }
    };

    // 添加全局键盘事件监听器 - 使用捕获阶段确保优先级，但精确排除输入框
    document.addEventListener('keydown', handleGlobalKeyDown, true);
    document.addEventListener('keyup', handleGlobalKeyUp, true);

    return () => {
      document.removeEventListener('keydown', handleGlobalKeyDown, true);
      document.removeEventListener('keyup', handleGlobalKeyUp, true);

      // 清理定时器
      stopContinuous();
      pressedKeysRef.current.clear();
    };
  }, [videoFiles.length, handleVideoTogglePlay, stepForward, stepBackward, startContinuousForward, startContinuousBackward, stopContinuous]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (continuousIntervalRef.current) {
        clearInterval(continuousIntervalRef.current);
      }
      if (longPressTimeoutRef.current) {
        clearTimeout(longPressTimeoutRef.current);
      }
    };
  }, []);

  // 剪辑模式控制
  const handleToggleClipMode = useCallback(() => {
    setIsClipMode(prev => {
      if (!prev) {
        // 进入剪辑模式时，设置默认的剪辑范围为整个视频
        setClipStartTime(0);
        setClipEndTime(masterDuration);
      }
      return !prev;
    });
  }, [masterDuration]);

  const handleClipTimeChange = useCallback((startTime: number, endTime: number) => {
    setClipStartTime(startTime);
    setClipEndTime(endTime);
  }, []);

  // 统一截图功能 - 统一按多视频处理
  const handleCaptureFrame = useCallback(async (currentVideoVisibility?: boolean[]) => {
    if (videoFiles.length === 0) return;

    try {
      // 使用传入的最新状态，如果没有传入则优先使用ref中的最新状态，最后才使用React状态
      const latestVideoVisibility = currentVideoVisibility || latestVideoVisibilityRef.current.length > 0 ? latestVideoVisibilityRef.current : videoVisibility;

      // 统一多视频截图逻辑
      // 获取当前的视频顺序（从 MultiVideoPlayer 组件）
      const currentVideoOrder = multiVideoPlayerRef.current?.getVideoOrder?.() || videoFiles;

      // 按照用户看到的顺序和可见性构建视频列表
      const orderedVisibleVideos = currentVideoOrder
        .map((file, displayIndex) => {
          const originalIndex = videoFiles.findIndex(f => f.id === file.id);
          const isVisible = latestVideoVisibility[originalIndex];
          return isVisible ? {
            ...file,
            displayOrder: displayIndex,
            originalIndex: originalIndex
          } : null;
        })
        .filter(Boolean); // 移除不可见的视频

      if (orderedVisibleVideos.length === 0) {
        console.error('videoVisibility状态异常，所有视频都不可见:', latestVideoVisibility);
        throw new Error('没有可见的视频可以截图');
      }

      console.log('📋 App截图视频顺序:', orderedVisibleVideos.map((v, i) =>
        `${i}: ${v.name} (原始索引: ${v.originalIndex}, 显示索引: ${v.displayOrder})`
      ));

      // 获取base64预览数据
      const result = await (window as any).electronAPI?.captureMultiVideoFrame?.({
        videoFiles: orderedVisibleVideos, // 传递按显示顺序排列的可见视频
        timestamp: masterTime,
        returnBase64: true, // 返回base64数据而不是创建临时文件
        preserveOrder: true // 标记保持传入的顺序
      });

      if (result?.success) {
        return {
          isMultiVideo: true, // 统一按多视频处理
          compositeScreenshot: {
            videoCount: orderedVisibleVideos.length, // 使用可见视频的数量
            timestamp: masterTime,
            base64Data: result.base64,
            resolution: result.resolution
          },
          timestamp: masterTime
        };
      } else {
        const errorMsg = result?.error || '截图预览失败，未知错误';
        console.error('截图预览失败，错误信息:', errorMsg);
        throw new Error(errorMsg);
      }
    } catch (error) {
      console.error('截图异常:', error);
      return null;
    }
  }, [videoFiles, masterTime, videoVisibility]);

  // 当视频加载完成后设置剪辑结束时间
  useEffect(() => {
    if (masterDuration > 0 && clipEndTime === 0) {
      setClipEndTime(masterDuration);
    }
  }, [masterDuration, clipEndTime]);

  // 监听剪辑进度
  useEffect(() => {
    const handleClipProgress = (event: any, progress: number) => {
      setClipProgress(progress);
    };

    const handleMultiVideoClipProgress = (event: any, progress: number) => {
      setClipProgress(progress);
    };

    // 添加剪辑进度监听器
    (window as any).electronAPI?.onClipProgress?.(handleClipProgress);
    (window as any).electronAPI?.onMultiVideoClipProgress?.(handleMultiVideoClipProgress);

    return () => {
      // 清理监听器
      (window as any).electronAPI?.removeClipProgressListener?.(handleClipProgress);
      (window as any).electronAPI?.removeMultiVideoClipProgressListener?.(handleMultiVideoClipProgress);
    };
  }, []);




  // 切换文件列表折叠状态
  const handleToggleFileListCollapse = useCallback(() => {
    setIsFileListCollapsed(prev => !prev);
  }, []);

  // 如果正在检查许可证，显示加载界面
  if (isCheckingLicense) {
    return (
      <Box
        display="flex"
        alignItems="center"
        justifyContent="center"
        height="100vh"
        bg="gray.50"
      >
        <Spinner size="xl" color="blue.500" />
        <Text ml={4} fontSize="lg" color="gray.600">
          正在验证许可证...
        </Text>
      </Box>
    );
  }

  // 如果未认证，显示验证码输入界面
  if (!isAuthenticated) {
    return <AuthCodeInput onAuthSuccess={handleAuthSuccess} />;
  }

  // Video.js 测试模式
  if (isVideoJSTestMode) {
    return (
      <Box>
        <Box p={4} bg="blue.50" borderBottom="1px" borderColor="blue.200">
          <Button
            onClick={() => setIsVideoJSTestMode(false)}
            colorScheme="blue"
            size="sm"
          >
            返回主应用
          </Button>
          <Text ml={4} display="inline" color="blue.800">
            Video.js 测试模式
          </Text>
        </Box>
        <SimpleVideoJSTest />
      </Box>
    );
  }

  return (
    <Box display="flex" height="100vh" overflow="hidden">
      {/* 左侧 - 视频文件管理组件，动态宽度 */}
      <VideoFileManager
        onFileSelect={handleFileSelect}
        onFilesUpdate={handleFilesUpdate}
        onMultiVideoPlay={handleVideoPlay}
        isCollapsed={isFileListCollapsed}
        onToggleCollapse={handleToggleFileListCollapse}
        onVideoJSTest={() => setIsVideoJSTestMode(true)}
      />

      {/* 右侧 - 主要内容区域 */}
      <Box
        flex={1}
        display="flex"
        flexDirection="column"
        height="100vh"
        overflow="hidden"
      >
        <Box
          display="flex"
          flexDirection="column"
          height="100%"
          p={3}
          bg="gray.50"
        >
            {/* 上半部分：视频播放器 + 地图GPS */}
            <Box
              display="flex"
              flex={1}
              minHeight="0"
              position="relative"
            >
              {/* 左侧：视频播放器 */}
              <Box
                width={`${videoAreaRatio * 100}%`}
                display="flex"
                flexDirection="column"
                minHeight="0"
                overflow="hidden"
                pr={1.5} // 右边距的一半
              >
                {/* 视频播放器容器 */}
                <Box
                  flex={1}
                  bg="white"
                  borderRadius="lg"
                  boxShadow="sm"
                  minHeight="0"
                  overflow="hidden"
                  display="flex"
                  flexDirection="column"
                >
                  <MultiVideoPlayer
                    ref={multiVideoPlayerRef}
                    videoFiles={videoFiles}
                    onTimeUpdate={handleTimeUpdate}
                    onClose={handleCloseVideo}
                    onSingleVideoPlay={handleSingleVideoPlay}
                    onPlay={handleVideoPlayControl}
                    onPause={handleVideoPause}
                    onTogglePlay={handleVideoTogglePlay}
                    onSeek={handleVideoSeek}
                    onVolumeChange={handleVideoVolumeChange}
                    onToggleMute={handleVideoToggleMute}
                    onSkipTime={handleVideoSkipTime}
                    isPlaying={isPlaying}
                    masterTime={masterTime}
                    masterDuration={masterDuration}
                    volume={volume}
                    isMuted={isMuted}
                    isSingleVideoMode={false} // 统一按多视频处理
                    showReturnToMultiVideo={previousMultiVideoFiles.length > 0}
                    onReturnToMultiVideo={handleReturnToMultiVideo}
                    onMainVideoFocusChange={handleMainVideoFocusChange}
                    onVisibleVideoCountChange={handleVisibleVideoCountChange}
                    onZoomStateChange={handleZoomStateChange}
                    videoVisibility={videoVisibility} // 传递视频可见性状态
                    onVideoVisibilityChange={handleVideoVisibilityChange}
                    // 剪辑进度相关
                    isClipping={isClipping}
                    clipProgress={clipProgress}
                  />
                </Box>
              </Box>

              {/* 拖拽分隔条 */}
              <Box
                width="8px"
                height="100%"
                position="relative"
                cursor="col-resize"
                onMouseDown={handleResizeStart}
                bg={isDragging ? "blue.100" : "transparent"}
                _hover={{
                  bg: "blue.100"
                }}
                transition="background-color 0.2s"
                display="flex"
                alignItems="center"
                justifyContent="center"
                borderRadius="md"
              >
                {/* 拖拽指示器 */}
                <Box
                  width="3px"
                  height="60px"
                  bg={isDragging ? "blue.400" : "gray.300"}
                  borderRadius="2px"
                  position="relative"
                  transition="background-color 0.2s"
                />

                {/* 占比提示 - 仅在拖拽时显示 */}
                {isDragging && (
                  <Box
                    position="absolute"
                    top="-30px"
                    left="50%"
                    transform="translateX(-50%)"
                    bg="gray.800"
                    color="white"
                    px={2}
                    py={1}
                    borderRadius="md"
                    fontSize="xs"
                    whiteSpace="nowrap"
                    zIndex={1000}
                    _after={{
                      content: '""',
                      position: "absolute",
                      top: "100%",
                      left: "50%",
                      transform: "translateX(-50%)",
                      width: 0,
                      height: 0,
                      borderLeft: "4px solid transparent",
                      borderRight: "4px solid transparent",
                      borderTop: "4px solid",
                      borderTopColor: "gray.800"
                    }}
                  >
                    视频 {Math.round(videoAreaRatio * 100)}% : GPS {Math.round((1 - videoAreaRatio) * 100)}%
                  </Box>
                )}
              </Box>

              {/* 右侧：地图和GPS信息 */}
              <Box
                width={`${(1 - videoAreaRatio) * 100}%`}
                display="flex"
                flexDirection="column"
                gap={3}
                flexShrink={0}
                pl={1.5} // 左边距的一半
              >
                <Box
                  height="45%"
                  bg="white"
                  position="relative"
                  borderRadius="lg"
                  overflow="hidden"
                  boxShadow="sm"
                >
                  {renderMapAndGPSInfo()}
                </Box>

                <Box
                  flex={1}
                  bg="white"
                  borderRadius="lg"
                  overflow="hidden"
                  boxShadow="sm"
                >
                  <GPSInfoPanel
                    currentGPSPoint={currentGPSPoint}
                    gpsTrack={currentGPSTrack}
                    isExtractingGPS={isExtractingGPS}
                    currentTime={_currentTime}
                    videoDuration={masterDuration}
                  />
                </Box>
              </Box>
            </Box>

            {/* 下半部分：视频控制栏 - 与整个右侧区域同宽 */}
            <Box
              flexShrink={0}
              mt={3}
            >
              <VideoPlayerControls
                isPlaying={isPlaying}
                currentTime={masterTime}
                duration={masterDuration}
                volume={volume}
                isMuted={isMuted}
                isFullscreen={false}
                onTogglePlay={() => {
                  if (isPlaying) {
                    handleVideoPause();
                  } else {
                    handleVideoPlayControl();
                  }
                }}
                onSeek={handleVideoSeek}
                onVolumeChange={handleVideoVolumeChange}
                onToggleMute={handleVideoToggleMute}
                onToggleFullscreen={() => {
                  // 视频全屏功能

                  multiVideoPlayerRef.current?.toggleFullscreen();
                }}
                onSkipTime={handleVideoSkipTime}
                // 添加缩放控制
                onToggleZoom={() => multiVideoPlayerRef.current?.toggleZoom?.()}
                scale={scale}
                showZoomControls={showZoomControls}
                onScaleChange={(scale) => multiVideoPlayerRef.current?.setScale?.(scale)}
                onResetZoom={() => multiVideoPlayerRef.current?.resetZoom?.()}
                videoCount={videoFiles.length}
                isMultiVideo={true} // 统一按多视频处理
                multiVideoExtensions={{
                  showProgressBar: true,
                  showTimeDisplay: true,
                  showVolumeSlider: false,
                  customButtons: undefined
                }}
                multiVideoFiles={videoFiles}
                videoVisibility={videoVisibility} // 传递实际的视频可见性状态
                onToggleVideoVisibility={(index) => {
                  // 调用 MultiVideoPlayer 的视频显示切换方法
                  multiVideoPlayerRef.current?.toggleVideoVisibility?.(index);
                }}
                showVolumeControl={true}
                showSkipButtons={true}
                showFullscreenButton={true}
                compact={false}
                // 返回多视频模式按钮
                showReturnToMultiVideo={isMainVideoFocused || (previousMultiVideoFiles.length > 0)}
                onReturnToMultiVideo={isMainVideoFocused ? handleReturnToMultiVideoLayout : handleReturnToMultiVideo}
                // 剪辑功能相关
                showClipControls={true}
                onCaptureFrame={handleCaptureFrame}
                videoPath={videoFiles.length > 0 ? videoFiles[0].path : undefined}
                onScreenshotTaken={(screenshot) => {}}
                videoFiles={videoFiles}
                // 剪辑模式相关
                isClipMode={isClipMode}
                clipStartTime={clipStartTime}
                clipEndTime={clipEndTime}
                onToggleClipMode={handleToggleClipMode}
                onClipTimeChange={handleClipTimeChange}
                onSaveClip={async () => {
                  // 统一剪辑保存功能
                  if (videoFiles.length === 0) return;

                  try {
                    setIsClipping(true);
                    setClipProgress(0);

                    // 验证时间设置
                    if (clipStartTime >= clipEndTime) {
                      setIsClipping(false);
                      return;
                    }

                    if (clipStartTime < 0 || clipEndTime > masterDuration) {
                      setIsClipping(false);
                      return;
                    }

                    // 统一多视频剪辑功能 - 使用用户看到的顺序和可见性
                    const currentVideoOrder = multiVideoPlayerRef.current?.getVideoOrder?.() || videoFiles;

                    // 按照用户看到的顺序和可见性构建视频列表
                    const orderedVisibleVideos = currentVideoOrder
                      .map((file, displayIndex) => {
                        const originalIndex = videoFiles.findIndex(f => f.id === file.id);
                        const isVisible = videoVisibility[originalIndex];
                        return isVisible ? {
                          ...file,
                          displayOrder: displayIndex,
                          originalIndex: originalIndex
                        } : null;
                      })
                      .filter(Boolean); // 移除不可见的视频

                    if (orderedVisibleVideos.length === 0) {
                      throw new Error('没有可见的视频可以剪辑');
                    }

                    console.log('📋 App剪辑视频顺序:', orderedVisibleVideos.map((v, i) =>
                      `${i}: ${v.name} (原始索引: ${v.originalIndex}, 显示索引: ${v.displayOrder})`
                    ));

                    const result = await (window as any).electronAPI?.clipMultiVideo?.({
                      videoFiles: orderedVisibleVideos, // 传递按显示顺序排列的可见视频
                      startTime: clipStartTime,
                      endTime: clipEndTime,
                      showSaveDialog: true, // 显示保存对话框
                      preserveOrder: true // 标记保持传入的顺序
                    });

                    if (result?.success) {
                      // 显示成功提示
                      toaster.create({
                        description: `已保存 (${(clipEndTime - clipStartTime).toFixed(1)}秒)`,
                        status: 'success',
                        duration: 3000,
                      });

                      setIsClipMode(false);
                    } else if (result?.cancelled) {
                      // 用户取消了保存操作，不显示错误
                      console.log('用户取消了剪辑保存');
                    } else {
                      throw new Error(result?.error || '剪辑失败');
                    }
                    setIsClipping(false);
                    setClipProgress(0);
                  } catch (error) {
                    console.error('剪辑失败:', error);
                    setIsClipping(false);
                    setClipProgress(0);
                  }
                }}
              />
            </Box>
          </Box>
      </Box>

      {/* App Key 配置弹窗 */}
      <ApiKeyConfigModal
        open={isConfigModalOpen}
        onOpenChange={(details: { open: boolean }) => setIsConfigModalOpen(details.open)}
        currentApiKey={amapApiKey}
        onApiKeyUpdate={handleApiKeyUpdate}
      />
    </Box>
  );
}

export default App;
