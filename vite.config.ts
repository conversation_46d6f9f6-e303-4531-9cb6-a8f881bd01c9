import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  base: './',
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    minify: 'esbuild',
    chunkSizeWarningLimit: 1000, // 提高警告阈值到1MB
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // 暂时禁用 React 的代码分割，避免 Children 属性问题
          // if (id.includes('react') || id.includes('react-dom')) {
          //   return 'react-vendor';
          // }

          // Chakra UI 相关
          if (id.includes('@chakra-ui') || id.includes('@emotion')) {
            return 'chakra-ui';
          }

          // 动画库
          if (id.includes('framer-motion')) {
            return 'motion';
          }

          // 图表库
          if (id.includes('recharts') || id.includes('d3')) {
            return 'charts';
          }

          // 工具库
          if (id.includes('lodash') || id.includes('date-fns') || id.includes('moment')) {
            return 'utils';
          }

          // 图标库
          if (id.includes('@tabler/icons') || id.includes('react-icons')) {
            return 'icons';
          }

          // Video.js 相关
          if (id.includes('video.js') || id.includes('videojs')) {
            return 'videojs';
          }

          // 其他第三方库（排除 React）
          if (id.includes('node_modules') && !id.includes('react')) {
            return 'vendor';
          }

          // 应用代码按功能分割
          if (id.includes('/components/')) {
            return 'components';
          }

          if (id.includes('/hooks/') || id.includes('/utils/')) {
            return 'app-utils';
          }
        }
      }
    }
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  },
  server: {
    port: 5174,
    host: 'localhost',
    strictPort: false
  },
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development')
  }
});
